// Copy from https://github.com/microsoft/TypeScript/blob/d85767abfd83880cea17cea70f9913e9c4496dcc/src/compiler/factory/emitHelpers.ts#L744-L753

function __decorateMetadata(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};

(module.exports = __decorateMetadata),
  (module.exports.__esModule = true),
  (module.exports["default"] = module.exports);
