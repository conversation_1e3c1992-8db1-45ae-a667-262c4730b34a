# @kdt310722/config

[![npm version][npm-version-src]][npm-version-href]
[![npm downloads][npm-downloads-src]][npm-downloads-href]
[![ci][ci-src]][ci-href]
[![coverage][coverage-src]][coverage-href]
[![issues][issues-src]][issues-href]
[![license][license-src]][license-href]

Simple config parser and validate using Zod for Javascript / Typescript project

## Usage

Install package:

```sh
# npm
npm install @kdt310722/config

# yarn
yarn add @kdt310722/config

# pnpm
pnpm install @kdt310722/config

# bun
bun install @kdt310722/config
```

Import:

```js
// ESM
import { defineConfig } from '@kdt310722/config'

// CommonJS
const { defineConfig } = require('@kdt310722/config')
```

Use:

```js
const config = defineConfig({
    key: z.string().default('value'),
})

console.log(config.parse())
```

## License

Published under [MIT License](LICENSE.md).

<!-- Badges -->

[npm-version-src]: https://img.shields.io/npm/v/@kdt310722/config?style=flat&colorA=1B3C4A&colorB=32A9C3&label=version
[npm-version-href]: https://npmjs.com/package/@kdt310722/config
[npm-downloads-src]: https://img.shields.io/npm/dm/@kdt310722/config?style=flat&colorA=1B3C4A&colorB=32A9C3&label=downloads
[npm-downloads-href]: https://npmjs.com/package/@kdt310722/config
[ci-src]: https://img.shields.io/github/actions/workflow/status/kdt310722/config/ci.yml?style=flat&colorA=1B3C4A&colorB=32A9C3&label=ci
[ci-href]: https://github.com/kdt310722/config/actions/workflows/ci.yml
[issues-src]: https://img.shields.io/github/issues/kdt310722/config?style=flat&colorA=1B3C4A&colorB=32A9C3&label=issues
[issues-href]: https://github.com/kdt310722/config/issues
[license-src]: https://img.shields.io/npm/l/@kdt310722/config?style=flat&colorA=1B3C4A&colorB=32A9C3&label=license
[license-href]: https://github.com/@kdt310722/config/blob/main/LICENSE.md
[coverage-src]: https://img.shields.io/codecov/c/gh/kdt310722/config?style=flat&colorA=1B3C4A&colorB=32A9C3
[coverage-href]: https://codecov.io/gh/kdt310722/config
