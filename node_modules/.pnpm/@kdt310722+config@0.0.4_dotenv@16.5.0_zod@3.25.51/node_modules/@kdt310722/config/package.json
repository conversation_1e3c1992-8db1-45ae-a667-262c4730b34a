{"name": "@kdt310722/config", "type": "module", "version": "0.0.4", "packageManager": "pnpm@9.15.2", "description": "Simple config parser and validate using Zod for Javascript / Typescript project", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/kdt310722/config", "repository": "github:kdt310722/config", "bugs": {"email": "<EMAIL>", "url": "https://github.com/kdt310722/config/issues"}, "sideEffects": false, "exports": {".": {"types": "./dist/types/index.d.ts", "require": "./dist/index.cjs", "default": "./dist/index.js"}}, "main": "dist/index.js", "types": "dist/types/index.d.ts", "files": ["dist", "src"], "publishConfig": {"access": "public"}, "scripts": {"dev": "vitest watch --typecheck", "build": "rimraf dist && tsup && tsc --project ./tsconfig.build.json", "test": "vitest run --typecheck", "coverage": "pnpm test -- --coverage", "release": "tsx scripts/release.ts && changelogen gh release && npm publish", "up": "ncu -i", "lint": "eslint .", "lint:fix": "eslint . --fix", "preinstall": "npx only-allow pnpm", "prepare": "simple-git-hooks", "prepublishOnly": "pnpm build"}, "peerDependencies": {"dotenv": "*", "zod": ">=3.24.0"}, "peerDependenciesMeta": {"dotenv": {"optional": true}}, "dependencies": {"@kdt310722/utils": "^0.0.12", "@types/minimist": "^1.2.5", "camelcase": "^8.0.0", "deepmerge": "^4.3.1", "flat": "^6.0.1", "minimist": "^1.2.8", "yaml": "^2.7.0", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@kdt310722/eslint-config": "^0.1.8", "@kdt310722/tsconfig": "^1.0.0", "@swc/core": "^1.10.4", "@types/node": "^22.10.3", "@vitest/coverage-v8": "^2.1.8", "changelogen": "^0.5.7", "eslint": "^9.17.0", "execa": "^9.5.2", "lint-staged": "^15.3.0", "npm-check-updates": "^17.1.13", "only-allow": "^1.2.1", "rimraf": "^6.0.1", "simple-git-hooks": "^2.11.1", "tsup": "^8.3.5", "tsx": "^4.19.2", "typescript": "^5.7.2", "vitest": "^2.1.8", "zod": "^3.24.1"}, "commitlint": {"extends": "@commitlint/config-conventional"}, "simple-git-hooks": {"commit-msg": "npx --no -- commitlint --edit ${1}", "pre-commit": "npx lint-staged"}, "lint-staged": {"*": "eslint --fix"}}