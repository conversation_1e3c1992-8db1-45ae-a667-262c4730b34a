{"version": 3, "sources": ["../src/configs/base/antfu.ts", "../src/configs/base/comments.ts", "../src/configs/base/ignores.ts", "../src/configs/base/import-x.ts", "../src/configs/base/javascript.ts", "../src/configs/base/jsonc.ts", "../src/configs/base/kdt.ts", "../package.json", "../src/plugins/kdt/rules/arrow-empty-body-newline.ts", "../src/utils/nodes.ts", "../src/utils/rules.ts", "../src/plugins/kdt/rules/import-single-line.ts", "../src/plugins/kdt/rules/object-curly-newline.ts", "../src/plugins/kdt/index.ts", "../src/configs/base/node.ts", "../src/configs/base/promise.ts", "../src/configs/base/regexp.ts", "../src/configs/base/stylistic.ts", "../src/configs/base/typescript.ts", "../src/configs/base/unused-imports.ts", "../src/configs/frameworks/tailwind.ts", "../src/configs/frameworks/vue.ts", "../src/configs/tools/format.ts", "../src/configs/tools/gitignore.ts", "../src/configs/tools/perfectionist.ts", "../src/configs/tools/sonar.ts", "../src/configs/tools/unicorn.ts", "../src/factory.ts"], "sourcesContent": ["import { concat } from 'eslint-flat-config-utils'\nimport plugin from 'eslint-plugin-antfu'\n\nexport const antfu = () => concat([\n    {\n        plugins: {\n            antfu: plugin,\n        },\n        rules: {\n            'antfu/if-newline': 'error',\n            'antfu/import-dedupe': 'error',\n            'antfu/no-import-dist': 'error',\n            'antfu/no-import-node-modules-by-path': 'error',\n        },\n    },\n    {\n        files: ['**/bin/**/*'],\n        rules: {\n            'antfu/no-import-dist': 'off',\n            'antfu/no-import-node-modules-by-path': 'off',\n        },\n    },\n])\n", "import configs from '@eslint-community/eslint-plugin-eslint-comments/configs'\nimport { concat } from 'eslint-flat-config-utils'\n\nexport const comments = () => concat(configs.recommended, {\n    files: ['**/*.d.ts'],\n    rules: {\n        '@eslint-community/eslint-comments/no-unlimited-disable': 'off',\n    },\n})\n", "import { concat } from 'eslint-flat-config-utils'\n\nexport const ignores = () => concat({\n    ignores: [\n        '**/*.min.*',\n        '**/*.tsbuildinfo',\n        '**/.astro',\n        '**/.cache',\n        '**/.changeset',\n        '**/.dockerignore',\n        '**/.env*',\n        '**/.history',\n        '**/.idea',\n        '**/.next',\n        '**/.nuxt',\n        '**/.nyc_output',\n        '**/.output',\n        '**/.parcel-cache',\n        '**/.pnpm',\n        '**/.rollup.cache',\n        '**/.rush',\n        '**/.secrets',\n        '**/.swc',\n        '**/.temp',\n        '**/.tmp',\n        '**/.tsbuildinfo',\n        '**/.turbo',\n        '**/.vercel',\n        '**/.vite',\n        '**/.vite-inspect',\n        '**/.vitepress/cache',\n        '**/.vscode',\n        '**/__snapshots__',\n        '**/auto-import?(s).d.ts',\n        '**/bun.lockb',\n        '**/CHANGELOG*.md',\n        '**/components.d.ts',\n        '**/coverage',\n        '**/dev-dist',\n        '**/dist',\n        '**/LICENSE*',\n        '**/node_modules',\n        '**/out',\n        '**/output',\n        '**/package-lock.json',\n        '**/playwright-report',\n        '**/pnpm-lock.yaml',\n        '**/pnpm-workspace.yaml',\n        '**/storybook-static',\n        '**/temp',\n        '**/test-results',\n        '**/tmp',\n        '**/tsconfig.tsbuildinfo',\n        '**/typed-router.d.ts',\n        '**/yarn.lock',\n    ],\n})\n", "import type { <PERSON><PERSON> } from 'eslint'\nimport { concat } from 'eslint-flat-config-utils'\nimport { importX as plugin } from 'eslint-plugin-import-x'\n\nconst pluginConfigs = [plugin.flatConfigs.recommended, plugin.flatConfigs.typescript] as Linter.Config[]\n\nexport const importX = () => concat(\n    ...pluginConfigs,\n    {\n        rules: {\n            'import-x/first': 'error',\n            'import-x/newline-after-import': ['error', { considerComments: true, count: 1 }],\n            'import-x/no-duplicates': 'error',\n            'import-x/no-empty-named-blocks': 'error',\n            'import-x/no-mutable-exports': 'error',\n            'import-x/no-named-as-default': 'off',\n            'import-x/no-named-as-default-member': 'off',\n            'import-x/no-named-default': 'error',\n            'import-x/no-self-import': 'error',\n            'import-x/no-unresolved': 'off',\n            'import-x/no-unused-modules': 'error',\n            'import-x/no-useless-path-segments': 'error',\n            'import-x/no-webpack-loader-syntax': 'error',\n            'import-x/order': 'error',\n        },\n    },\n    {\n        files: ['**/*.d.ts'],\n        rules: {\n            'import-x/no-duplicates': 'off',\n        },\n    },\n)\n", "import type { <PERSON><PERSON> } from 'eslint'\nimport js from '@eslint/js'\nimport { concat } from 'eslint-flat-config-utils'\nimport globals from 'globals'\n\nconst rules: Linter.RulesRecord = {\n    'accessor-pairs': ['error', { enforceForClassMembers: true, setWithoutGet: true }],\n    'array-callback-return': 'error',\n    'block-scoped-var': 'error',\n    'curly': ['error', 'all'],\n    'default-case-last': 'error',\n    'default-param-last': 'off',\n    'eqeqeq': ['error', 'smart'],\n    'grouped-accessor-pairs': 'error',\n    'logical-assignment-operators': 'error',\n    'new-cap': 'off',\n    'no-alert': 'error',\n    'no-array-constructor': 'error',\n    'no-caller': 'error',\n    'no-console': ['error', { allow: ['warn', 'error'] }],\n    'no-else-return': ['error', { allowElseIf: true }],\n    'no-empty': ['error', { allowEmptyCatch: true }],\n    'no-eval': 'error',\n    'no-extend-native': 'error',\n    'no-extra-bind': 'error',\n    'no-extra-label': 'error',\n    'no-fallthrough': ['error', { allowEmptyCase: true, commentPattern: String.raw`break[\\s\\w]*omitted` }],\n    'no-implied-eval': 'error',\n    'no-iterator': 'error',\n    'no-label-var': 'error',\n    'no-labels': ['error', { allowLoop: false, allowSwitch: false }],\n    'no-lone-blocks': 'error',\n    'no-multi-str': 'error',\n    'no-new': 'error',\n    'no-new-func': 'error',\n    'no-new-wrappers': 'error',\n    'no-octal-escape': 'error',\n    'no-proto': 'error',\n    'no-redeclare': ['error', { builtinGlobals: false }],\n    'no-restricted-globals': ['error', { message: 'Use `globalThis` instead.', name: 'global' }, { message: 'Use `globalThis` instead.', name: 'self' }],\n    'no-restricted-properties': ['error', { message: 'Use `Object.getPrototypeOf` or `Object.setPrototypeOf` instead.', property: '__proto__' }, { message: 'Use `Object.defineProperty` instead.', property: '__defineGetter__' }, { message: 'Use `Object.defineProperty` instead.', property: '__defineSetter__' }, { message: 'Use `Object.getOwnPropertyDescriptor` instead.', property: '__lookupGetter__' }, { message: 'Use `Object.getOwnPropertyDescriptor` instead.', property: '__lookupSetter__' }],\n    'no-restricted-syntax': ['error', 'DebuggerStatement', 'LabeledStatement', 'WithStatement', 'TSEnumDeclaration[const=true]', 'TSExportAssignment'],\n    'no-self-compare': 'error',\n    'no-sequences': 'error',\n    'no-template-curly-in-string': 'error',\n    'no-throw-literal': 'error',\n    'no-undef-init': 'error',\n    'no-unmodified-loop-condition': 'error',\n    'no-unneeded-ternary': ['error', { defaultAssignment: false }],\n    'no-unreachable-loop': 'error',\n    'no-unused-expressions': ['error', { allowShortCircuit: true, allowTaggedTemplates: true, allowTernary: true }],\n    'no-unused-vars': ['error', { args: 'none', caughtErrors: 'none', ignoreRestSiblings: true, vars: 'all' }],\n    'no-use-before-define': ['error', { classes: false, functions: false, variables: true }],\n    'no-useless-call': 'error',\n    'no-useless-computed-key': ['error', { enforceForClassMembers: true }],\n    'no-useless-concat': 'error',\n    'no-useless-constructor': 'error',\n    'no-useless-rename': 'error',\n    'no-useless-return': 'error',\n    'no-var': 'error',\n    'object-shorthand': ['error', 'properties', { avoidQuotes: true }],\n    'one-var': ['error', { initialized: 'never' }],\n    'operator-assignment': 'error',\n    'prefer-arrow-callback': ['error', { allowNamedFunctions: false, allowUnboundThis: true }],\n    'prefer-const': ['error', { destructuring: 'all', ignoreReadBeforeAssign: true }],\n    'prefer-exponentiation-operator': 'error',\n    'prefer-promise-reject-errors': ['error', { allowEmptyReject: true }],\n    'prefer-regex-literals': ['error', { disallowRedundantWrapping: true }],\n    'prefer-rest-params': 'error',\n    'prefer-spread': 'error',\n    'prefer-template': 'error',\n    'sort-imports': ['error', { allowSeparatedGroups: false, ignoreCase: false, ignoreDeclarationSort: true, ignoreMemberSort: false, memberSyntaxSortOrder: ['none', 'all', 'multiple', 'single'] }],\n    'symbol-description': 'error',\n    'unicode-bom': 'error',\n    'vars-on-top': 'error',\n    'yoda': 'error',\n}\n\nconst config: Linter.Config = {\n    languageOptions: {\n        sourceType: 'module',\n        ecmaVersion: 2022,\n        globals: {\n            ...globals.browser,\n            ...globals.es2021,\n            ...globals.node,\n            document: 'readonly',\n            navigator: 'readonly',\n            window: 'readonly',\n        },\n        parserOptions: {\n            ecmaFeatures: { jsx: true },\n            ecmaVersion: 2022,\n            sourceType: 'module',\n        },\n    },\n    linterOptions: {\n        reportUnusedDisableDirectives: true,\n    },\n    rules,\n}\n\nconst configForScriptsFolder: Linter.Config = {\n    files: [`scripts/**/*.?([cm])[jt]s?(x)`],\n    rules: {\n        'no-console': 'off',\n    },\n}\n\nconst configForTestFiles: Linter.Config = {\n    files: ['**/*.{test,spec}.?([cm])[jt]s?(x)'],\n    rules: {\n        'no-unused-expressions': 'off',\n    },\n}\n\nexport const javascript = () => concat(js.configs.recommended as Linter.Config, config, configForScriptsFolder, configForTestFiles)\n", "import type { Lin<PERSON> } from 'eslint'\nimport { concat } from 'eslint-flat-config-utils'\nimport plugin from 'eslint-plugin-jsonc'\n\nexport const jsonc = () => concat(\n    plugin.configs['flat/recommended-with-json'] as Linter.Config,\n    plugin.configs['flat/recommended-with-json5'],\n    plugin.configs['flat/recommended-with-jsonc'],\n    {\n        rules: {\n            'jsonc/comma-dangle': ['error', 'never'],\n            'jsonc/quotes': ['error', 'double'],\n        },\n    },\n    {\n        files: ['**/package.json'],\n        rules: {\n            'jsonc/sort-array-values': ['error', { order: { type: 'asc' }, pathPattern: '^files$' }],\n            'jsonc/sort-keys': ['error', { order: ['publisher', 'name', 'displayName', 'type', 'version', 'private', 'packageManager', 'description', 'author', 'license', 'man', 'directories', 'funding', 'homepage', 'repository', 'bugs', 'keywords', 'os', 'cpu', 'categories', 'sideEffects', 'exports', 'main', 'module', 'unpkg', 'jsdelivr', 'types', 'typesVersions', 'bin', 'icon', 'files', 'engines', 'workspaces', 'volta', 'activationEvents', 'contributes', 'scripts', 'peerDependencies', 'peerDependenciesMeta', 'dependencies', 'optionalDependencies', 'devDependencies', 'bundledDependencies', 'dependenciesMeta', 'pnpm', 'publishConfig', 'config', 'overrides', 'resolutions', 'husky', 'simple-git-hooks', 'lint-staged', 'eslintConfig', 'preferGlobal'], pathPattern: '^$' }, { order: { type: 'asc' }, pathPattern: '^(?:dev|peer|optional|bundled)?[Dd]ependencies(Meta)?$' }, { order: { type: 'asc' }, pathPattern: '^(?:resolutions|overrides|pnpm.overrides)$' }, { order: ['types', 'import', 'require', 'default'], pathPattern: '^exports.*$' }],\n        },\n    },\n    {\n        files: ['**/tsconfig.json', '**/tsconfig.*.json'],\n        rules: {\n            'jsonc/sort-keys': ['error', { order: ['extends', 'compilerOptions', 'compileOnSave', 'watchOptions', 'buildOptions', 'references', 'files', 'include', 'exclude', 'ts-node'], pathPattern: '^$' }, { order: ['incremental', 'composite', 'tsBuildInfoFile', 'disableSourceOfProjectReferenceRedirect', 'disableSolutionSearching', 'disableReferencedProjectLoad', 'target', 'jsx', 'jsxFactory', 'jsxFragmentFactory', 'jsxImportSource', 'lib', 'moduleDetection', 'noLib', 'reactNamespace', 'useDefineForClassFields', 'emitDecoratorMetadata', 'experimentalDecorators', 'baseUrl', 'rootDir', 'rootDirs', 'customConditions', 'module', 'moduleResolution', 'moduleSuffixes', 'noResolve', 'paths', 'resolveJsonModule', 'resolvePackageJsonExports', 'resolvePackageJsonImports', 'typeRoots', 'types', 'allowArbitraryExtensions', 'allowImportingTsExtensions', 'allowUmdGlobalAccess', 'ignoreDeprecations', 'allowJs', 'checkJs', 'maxNodeModuleJsDepth', 'strict', 'strictBindCallApply', 'strictFunctionTypes', 'strictNullChecks', 'strictPropertyInitialization', 'allowUnreachableCode', 'allowUnusedLabels', 'alwaysStrict', 'exactOptionalPropertyTypes', 'noFallthroughCasesInSwitch', 'noImplicitAny', 'noImplicitOverride', 'noImplicitReturns', 'noImplicitThis', 'noPropertyAccessFromIndexSignature', 'noUncheckedIndexedAccess', 'noUnusedLocals', 'noUnusedParameters', 'useUnknownInCatchVariables', 'declaration', 'declarationDir', 'declarationMap', 'downlevelIteration', 'emitBOM', 'emitDeclarationOnly', 'importHelpers', 'importsNotUsedAsValues', 'inlineSourceMap', 'inlineSources', 'mapRoot', 'newLine', 'noEmit', 'noEmitHelpers', 'noEmitOnError', 'outDir', 'outFile', 'preserveConstEnums', 'preserveValueImports', 'removeComments', 'sourceMap', 'sourceRoot', 'stripInternal', 'allowSyntheticDefaultImports', 'esModuleInterop', 'forceConsistentCasingInFileNames', 'isolatedModules', 'preserveSymlinks', 'verbatimModuleSyntax', 'skipDefaultLibCheck', 'skipLibCheck'], pathPattern: '^compilerOptions$' }],\n        },\n    },\n)\n", "import { concat } from 'eslint-flat-config-utils'\nimport { kdtPlugin } from '../../plugins'\n\nexport const kdt = () => concat({\n    plugins: {\n        kdt: kdtPlugin,\n    },\n    rules: {\n        'kdt/arrow-empty-body-newline': 'error',\n        'kdt/import-single-line': 'error',\n        'kdt/object-curly-newline': 'error',\n        '@stylistic/object-curly-newline': 'off',\n    },\n})\n", "{\n    \"name\": \"@kdt-farm/eslint-config\",\n    \"type\": \"module\",\n    \"version\": \"0.0.2\",\n    \"packageManager\": \"pnpm@10.11.0\",\n    \"description\": \"ESLint configuration for TypeScript projects\",\n    \"author\": \"Die<PERSON> Dang <<EMAIL>>\",\n    \"license\": \"MIT\",\n    \"homepage\": \"https://github.com/kdt-farm/eslint-config\",\n    \"repository\": \"github:kdt-farm/eslint-config\",\n    \"bugs\": {\n        \"email\": \"<EMAIL>\",\n        \"url\": \"https://github.com/kdt-farm/eslint-config/issues\"\n    },\n    \"sideEffects\": false,\n    \"exports\": {\n        \".\": {\n            \"types\": \"./dist/types/index.d.ts\",\n            \"require\": \"./dist/index.cjs\",\n            \"default\": \"./dist/index.js\"\n        }\n    },\n    \"main\": \"dist/index.js\",\n    \"types\": \"dist/types/index.d.ts\",\n    \"files\": [\n        \"dist\",\n        \"src\"\n    ],\n    \"scripts\": {\n        \"inspect\": \"eslint-config-inspector\",\n        \"build\": \"rimraf dist && tsup && tsc --project ./tsconfig.build.json\",\n        \"release\": \"tsx scripts/release.ts && changelogen gh release && pnpm publish\",\n        \"up\": \"ncu -i\",\n        \"lint\": \"eslint .\",\n        \"lint:fix\": \"eslint . --fix\",\n        \"typecheck\": \"tsc --noEmit\",\n        \"preinstall\": \"npx only-allow pnpm\",\n        \"prepare\": \"simple-git-hooks\",\n        \"prepublishOnly\": \"pnpm build\"\n    },\n    \"peerDependencies\": {\n        \"eslint\": \">=9\"\n    },\n    \"dependencies\": {\n        \"@eslint-community/eslint-plugin-eslint-comments\": \"^4.5.0\",\n        \"@eslint/js\": \"^9.28.0\",\n        \"@kdt310722/utils\": \"^0.0.19\",\n        \"@stylistic/eslint-plugin\": \"^4.4.0\",\n        \"@typescript-eslint/parser\": \"^8.33.0\",\n        \"@typescript-eslint/utils\": \"^8.33.0\",\n        \"eslint-config-flat-gitignore\": \"^2.1.0\",\n        \"eslint-flat-config-utils\": \"^2.1.0\",\n        \"eslint-import-resolver-typescript\": \"^4.4.2\",\n        \"eslint-merge-processors\": \"^2.0.0\",\n        \"eslint-plugin-antfu\": \"^3.1.1\",\n        \"eslint-plugin-better-tailwindcss\": \"^3.0.0\",\n        \"eslint-plugin-command\": \"^3.2.1\",\n        \"eslint-plugin-format\": \"^1.0.1\",\n        \"eslint-plugin-import-x\": \"^4.15.0\",\n        \"eslint-plugin-jsonc\": \"^2.20.1\",\n        \"eslint-plugin-n\": \"^17.18.0\",\n        \"eslint-plugin-perfectionist\": \"^4.13.0\",\n        \"eslint-plugin-promise\": \"^7.2.1\",\n        \"eslint-plugin-regexp\": \"^2.7.0\",\n        \"eslint-plugin-sonarjs\": \"^3.0.2\",\n        \"eslint-plugin-unicorn\": \"^59.0.1\",\n        \"eslint-plugin-unused-imports\": \"^4.1.4\",\n        \"eslint-plugin-vue\": \"^10.1.0\",\n        \"eslint-plugin-vuejs-accessibility\": \"^2.4.1\",\n        \"eslint-processor-vue-blocks\": \"^2.0.0\",\n        \"globals\": \"^16.2.0\",\n        \"prettier\": \"^3.5.3\",\n        \"prettier-plugin-tailwindcss\": \"^0.6.12\",\n        \"typescript-eslint\": \"^8.33.0\",\n        \"vue-eslint-parser\": \"^10.1.3\"\n    },\n    \"devDependencies\": {\n        \"@commitlint/cli\": \"^19.8.1\",\n        \"@commitlint/config-conventional\": \"^19.8.1\",\n        \"@eslint/config-inspector\": \"^1.0.2\",\n        \"@kdt310722/tsconfig\": \"^1.0.0\",\n        \"@swc/core\": \"^1.11.29\",\n        \"@types/node\": \"^22.15.29\",\n        \"changelogen\": \"^0.6.1\",\n        \"eslint\": \"^9.28.0\",\n        \"execa\": \"^9.6.0\",\n        \"jiti\": \"^2.4.2\",\n        \"lint-staged\": \"^16.1.0\",\n        \"npm-check-updates\": \"^18.0.1\",\n        \"only-allow\": \"^1.2.1\",\n        \"rimraf\": \"^6.0.1\",\n        \"simple-git-hooks\": \"^2.13.0\",\n        \"tsup\": \"^8.5.0\",\n        \"tsx\": \"^4.19.4\",\n        \"typescript\": \"^5.8.3\"\n    },\n    \"publishConfig\": {\n        \"access\": \"public\"\n    },\n    \"commitlint\": {\n        \"extends\": \"@commitlint/config-conventional\"\n    },\n    \"simple-git-hooks\": {\n        \"commit-msg\": \"npx --no -- commitlint --edit ${1}\",\n        \"pre-commit\": \"npx lint-staged\"\n    },\n    \"lint-staged\": {\n        \"*\": \"eslint --fix\"\n    }\n}\n", "import { AST_NODE_TYPES, ASTUtils } from '@typescript-eslint/utils'\nimport { createRule, isBlockStatement } from '../../../utils'\n\nexport const arrowEmptyBodyNewline = createRule({\n    meta: {\n        type: 'layout',\n        fixable: 'whitespace',\n        schema: [],\n        messages: {\n            unexpectedNewLine: 'Unexpected newline between empty arrow function body',\n        },\n    },\n    defaultOptions: [],\n    create({ sourceCode, report }) {\n        return {\n            [AST_NODE_TYPES.ArrowFunctionExpression](node) {\n                const inlineComments = sourceCode.getCommentsInside(node.body)\n\n                if (inlineComments.length === 0 && isBlockStatement(node.body) && node.body.body.length === 0) {\n                    const openBracket = sourceCode.getFirstToken(node.body)\n                    const closeBracket = sourceCode.getLastToken(node.body)\n\n                    if (openBracket && closeBracket && !ASTUtils.isTokenOnSameLine(openBracket, closeBracket)) {\n                        report({ node, loc: node.body.loc, messageId: 'unexpectedNewLine', fix: (fixer) => fixer.replaceTextRange(node.body.range, '{}') })\n                    }\n                }\n            },\n        }\n    },\n})\n", "import type { TSESTree } from '@typescript-eslint/utils'\nimport { AST_NODE_TYPES } from '@typescript-eslint/utils'\n\nexport function isBlockStatement(node: TSESTree.Node | undefined): node is TSESTree.BlockStatement {\n    return node?.type === AST_NODE_TYPES.BlockStatement\n}\n", "import type { Rule as ESLintRule } from 'eslint'\nimport { RuleCreator, type RuleWithMeta } from '@typescript-eslint/utils/eslint-utils'\n\nexport type Rule<O extends readonly unknown[], M extends string> = Readonly<RuleWithMeta<O, M>>\n\nexport type RuleModule<O extends readonly unknown[]> = ESLintRule.RuleModule & {\n    defaultOptions: O\n}\n\nexport function createRule<O extends readonly unknown[], M extends string>(rule: Rule<O, M>) {\n    return RuleCreator.withoutDocs(rule as any) as unknown as RuleModule<O>\n}\n", "import { AST_NODE_TYPES } from '@typescript-eslint/utils'\nimport { createRule } from '../../../utils'\n\nconst LINE_BREAK_REGEX = /[\\n\\r]+/\n\nexport const importSingleLine = createRule({\n    meta: {\n        type: 'layout',\n        fixable: 'code',\n        schema: [],\n        messages: { unexpectedLineBreak: 'Remove line break in the import statement' },\n    },\n    defaultOptions: [],\n    create({ sourceCode, report }) {\n        return {\n            [AST_NODE_TYPES.ImportDeclaration](node) {\n                const source = sourceCode.getText(node)\n\n                if (!LINE_BREAK_REGEX.test(source)) {\n                    return\n                }\n\n                report({ node, messageId: 'unexpectedLineBreak', fix: (fixer) => fixer.replaceText(node, source.replace(LINE_BREAK_REGEX, '')) })\n            },\n        }\n    },\n})\n", "import stylistic from '@stylistic/eslint-plugin'\nimport { ASTUtils } from '@typescript-eslint/utils'\nimport { createRule, type Rule } from '../../../utils'\n\ntype OptionValues = 'always' | 'never'\n\nexport interface OptionObject {\n    multiline?: boolean\n    minProperties?: number\n    consistent?: boolean\n}\n\ntype OptionObjectOrValues = OptionValues | OptionObject\n\nexport interface OptionLiterals {\n    ObjectExpression?: OptionObjectOrValues\n    ObjectPattern?: OptionObjectOrValues\n    ImportDeclaration?: OptionObjectOrValues\n    ExportDeclaration?: OptionObjectOrValues\n}\n\ntype Options = OptionObjectOrValues | OptionLiterals\ntype RuleOptions = [Options?]\n\nexport type ObjectCurlyNewlineRuleOptions = RuleOptions\n\nconst baseRule = stylistic.rules['object-curly-newline'] as unknown as Rule<RuleOptions, string>\n\nexport const objectCurlyNewline = createRule<RuleOptions, string>({\n    meta: baseRule.meta,\n    defaultOptions: [\n        {\n            ObjectExpression: { multiline: true },\n            ObjectPattern: { multiline: true, consistent: true },\n            ImportDeclaration: 'never',\n            ExportDeclaration: 'never',\n        },\n    ],\n    create(context, [options]) {\n        function getRules(opts?: Options) {\n            const contextWithDefaults = Object.create(context, {\n                options: { writable: false, configurable: false, value: [opts] },\n            })\n\n            return baseRule.create(contextWithDefaults, [opts])\n        }\n\n        function getMinProperties() {\n            if (!options || typeof options === 'string') {\n                return false\n            }\n\n            if ('multiline' in options && options.multiline) {\n                return options.minProperties ?? Number.POSITIVE_INFINITY\n            }\n\n            if (!('ObjectExpression' in options) || typeof options.ObjectExpression === 'string') {\n                return false\n            }\n\n            if (!options.ObjectExpression?.multiline) {\n                return false\n            }\n\n            return options.ObjectExpression.minProperties ?? Number.POSITIVE_INFINITY\n        }\n\n        const rules = getRules(options)\n        const minProperties = getMinProperties()\n\n        return {\n            ...rules,\n            ObjectExpression(node) {\n                if (minProperties === false || node.properties.length >= minProperties) {\n                    return rules.ObjectExpression?.(node)\n                }\n\n                const source = context.sourceCode\n                const openBrace = source.getFirstToken(node, (token) => token.value === '{')\n                const closeBrace = source.getLastToken(node, (token) => token.value === '}')\n\n                if (!openBrace || !closeBrace) {\n                    return rules.ObjectExpression?.(node)\n                }\n\n                let first = source.getTokenAfter(openBrace, { includeComments: true })\n                let last = source.getTokenBefore(closeBrace, { includeComments: true })\n\n                if (!first || !last) {\n                    return rules.ObjectExpression?.(node)\n                }\n\n                if (!(node.properties.length > 0 && first.loc.start.line !== last.loc.end.line)) {\n                    first = source.getTokenAfter(openBrace)\n                    last = source.getTokenBefore(closeBrace)\n\n                    if (!first || !last) {\n                        return rules.ObjectExpression?.(node)\n                    }\n\n                    const hasLineBreakBetweenOpenBraceAndFirst = !ASTUtils.isTokenOnSameLine(openBrace, first)\n                    const hasLineBreakBetweenCloseBraceAndLast = !ASTUtils.isTokenOnSameLine(last, closeBrace)\n\n                    if (hasLineBreakBetweenOpenBraceAndFirst || hasLineBreakBetweenCloseBraceAndLast) {\n                        return getRules('always').ObjectExpression?.(node)\n                    }\n                }\n\n                return rules.ObjectExpression?.(node)\n            },\n        }\n    },\n})\n", "import type { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'eslint'\nimport { version } from '../../../package.json'\nimport { arrowEmptyBodyNewline, importSingleLine, objectCurlyNewline } from './rules'\n\nconst plugin = {\n    meta: {\n        name: 'kdt',\n        version,\n    },\n    rules: {\n        'arrow-empty-body-newline': arrowEmptyBodyNewline,\n        'import-single-line': importSingleLine,\n        'object-curly-newline': objectCurlyNewline,\n    },\n} satisfies ESLint.Plugin\n\nexport default plugin\n\ntype RuleDefinitions = typeof plugin['rules']\n\nexport type KDTRuleOptions = {\n    [K in keyof RuleDefinitions]: RuleDefinitions[K]['defaultOptions']\n}\n\nexport type KDTRules = {\n    [K in keyof KDTRuleOptions]: Linter.RuleEntry<KDTRuleOptions[K]>\n}\n", "import { concat } from 'eslint-flat-config-utils'\nimport plugin from 'eslint-plugin-n'\n\nexport const node = () => concat(plugin.configs['flat/recommended'], {\n    rules: {\n        'n/handle-callback-err': ['error', '^(err|error)$'],\n        'n/hashbang': 'off',\n        'n/no-missing-import': 'off',\n        'n/no-missing-require': 'off',\n        'n/no-new-require': 'error',\n        'n/no-path-concat': 'error',\n        'n/no-process-exit': 'off',\n        'n/no-unpublished-import': 'off',\n        'n/no-unpublished-require': 'off',\n        'n/no-unsupported-features/node-builtins': 'off',\n        'n/shebang': 'off',\n    },\n})\n", "import { concat } from 'eslint-flat-config-utils'\nimport plugin from 'eslint-plugin-promise'\n\nexport const promise = () => concat(plugin.configs['flat/recommended'], {\n    rules: {\n        'promise/always-return': 'off',\n        'promise/catch-or-return': 'off',\n        'promise/no-multiple-resolved': 'error',\n        'promise/no-nesting': 'off',\n        'promise/no-promise-in-callback': 'off',\n    },\n})\n", "import { concat } from 'eslint-flat-config-utils'\nimport * as plugin from 'eslint-plugin-regexp'\n\nexport const regexp = () => concat(plugin.configs['flat/recommended'])\n", "import type { Lin<PERSON> } from 'eslint'\nimport stylisticPlugin from '@stylistic/eslint-plugin'\nimport { concat } from 'eslint-flat-config-utils'\n\nconst commaDangleConfig = {\n    arrays: 'always-multiline',\n    exports: 'never',\n    functions: 'always-multiline',\n    imports: 'never',\n    objects: 'always-multiline',\n    enums: 'always-multiline',\n    generics: 'never',\n    tuples: 'never',\n}\n\nconst linesAroundCommentConfig = {\n    allowArrayStart: true,\n    allowBlockStart: true,\n    allowClassStart: true,\n    allowObjectStart: true,\n    beforeBlockComment: true,\n    beforeLineComment: true,\n}\n\nconst paddingLineBetweenStatementsConfig = [\n    { blankLine: 'never', next: ['break', 'default'], prev: '*' },\n    { blankLine: 'never', next: '*', prev: ['break', 'case', 'default'] },\n    { blankLine: 'never', next: 'case', prev: 'switch' },\n    { blankLine: 'always', next: 'interface', prev: '*' },\n    { blankLine: 'always', next: '*', prev: 'interface' },\n    { blankLine: 'always', next: 'class', prev: '*' },\n    { blankLine: 'always', next: '*', prev: 'class' },\n    { blankLine: 'always', next: '*', prev: 'directive' },\n    { blankLine: 'always', next: '*', prev: ['do', 'for', 'while'] },\n    { blankLine: 'always', next: ['do', 'for', 'while'], prev: '*' },\n    { blankLine: 'always', next: '*', prev: 'function' },\n    { blankLine: 'always', next: 'function', prev: 'directive' },\n    { blankLine: 'always', next: '*', prev: 'if' },\n    { blankLine: 'always', next: 'if', prev: '*' },\n    { blankLine: 'always', next: '*', prev: ['multiline-block-like', 'multiline-expression'] },\n    { blankLine: 'always', next: ['multiline-block-like', 'multiline-expression'], prev: '*' },\n    { blankLine: 'always', next: '*', prev: ['multiline-const', 'multiline-let', 'multiline-var'] },\n    { blankLine: 'always', next: ['multiline-const', 'multiline-let', 'multiline-var'], prev: '*' },\n    { blankLine: 'always', next: 'return', prev: '*' },\n    { blankLine: 'always', next: '*', prev: 'switch' },\n    { blankLine: 'always', next: 'switch', prev: '*' },\n    { blankLine: 'always', next: '*', prev: 'try' },\n    { blankLine: 'always', next: 'try', prev: '*' },\n    { blankLine: 'always', next: '*', prev: 'with' },\n    { blankLine: 'always', next: 'with', prev: '*' },\n]\n\nconst plugin = stylisticPlugin.configs.customize({\n    indent: 4,\n    arrowParens: true,\n    braceStyle: '1tbs',\n    quoteProps: 'consistent',\n})\n\nlet configs: Promise<Linter.Config[]> | undefined\n\nexport const stylistic = () => configs ??= concat(stylisticPlugin.configs['disable-legacy'], plugin, {\n    rules: {\n        '@stylistic/array-bracket-newline': ['error', 'consistent'],\n        '@stylistic/array-element-newline': ['error', 'consistent'],\n        '@stylistic/comma-dangle': ['error', commaDangleConfig],\n        '@stylistic/func-call-spacing': 'error',\n        '@stylistic/function-call-argument-newline': ['error', 'consistent'],\n        '@stylistic/function-call-spacing': 'error',\n        '@stylistic/function-paren-newline': ['error', 'multiline-arguments'],\n        '@stylistic/generator-star-spacing': ['error', 'both'],\n        '@stylistic/implicit-arrow-linebreak': 'error',\n        '@stylistic/jsx-child-element-spacing': 'off',\n        '@stylistic/jsx-newline': 'off',\n        '@stylistic/jsx-props-no-multi-spaces': 'off',\n        '@stylistic/jsx-quotes': ['error', 'prefer-double'],\n        '@stylistic/jsx-self-closing-comp': 'off',\n        '@stylistic/jsx-sort-props': 'off',\n        '@stylistic/linebreak-style': ['error', 'unix'],\n        '@stylistic/lines-around-comment': ['error', linesAroundCommentConfig],\n        '@stylistic/multiline-ternary': ['error', 'never'],\n        '@stylistic/newline-per-chained-call': 'off',\n        '@stylistic/no-confusing-arrow': 'error',\n        '@stylistic/no-extra-semi': 'error',\n        '@stylistic/nonblock-statement-body-position': ['error', 'below'],\n        '@stylistic/object-curly-newline': ['error', { consistent: true, multiline: true }],\n        '@stylistic/object-property-newline': ['error', { allowAllPropertiesOnSameLine: true }],\n        '@stylistic/one-var-declaration-per-line': 'off',\n        '@stylistic/operator-linebreak': ['error', 'after'],\n        '@stylistic/padding-line-between-statements': ['error', ...paddingLineBetweenStatementsConfig],\n        '@stylistic/semi-style': 'error',\n        '@stylistic/switch-colon-spacing': 'error',\n        '@stylistic/wrap-regex': 'off',\n        '@stylistic/max-len': 'off',\n    },\n})\n", "import type { <PERSON><PERSON> } from 'eslint'\nimport { concat } from 'eslint-flat-config-utils'\nimport tseslint from 'typescript-eslint'\n\nconst rules: Linter.RulesRecord = {\n    'no-use-before-define': 'off',\n    '@typescript-eslint/array-type': ['error', { default: 'array-simple' }],\n    '@typescript-eslint/ban-ts-comment': ['error', { 'ts-ignore': 'allow-with-description' }],\n    '@typescript-eslint/consistent-type-assertions': 'off',\n    '@typescript-eslint/consistent-type-definitions': 'off',\n    '@typescript-eslint/consistent-type-imports': ['error', { disallowTypeAnnotations: false, prefer: 'type-imports', fixStyle: 'inline-type-imports' }],\n    '@typescript-eslint/dot-notation': 'off',\n    '@typescript-eslint/no-confusing-void-expression': 'off',\n    '@typescript-eslint/no-dynamic-delete': 'off',\n    '@typescript-eslint/no-empty-function': 'off',\n    '@typescript-eslint/no-explicit-any': 'off',\n    '@typescript-eslint/no-extraneous-class': 'off',\n    '@typescript-eslint/no-import-type-side-effects': 'error',\n    '@typescript-eslint/no-invalid-void-type': 'off',\n    '@typescript-eslint/no-misused-promises': 'off',\n    '@typescript-eslint/no-non-null-assertion': 'off',\n    '@typescript-eslint/no-redundant-type-constituents': 'off',\n    '@typescript-eslint/no-unnecessary-condition': 'off',\n    '@typescript-eslint/no-unused-vars': ['error', { args: 'all', argsIgnorePattern: '^_', caughtErrors: 'all', caughtErrorsIgnorePattern: '^_', destructuredArrayIgnorePattern: '^_', vars: 'all', varsIgnorePattern: '^_', ignoreRestSiblings: true }],\n    '@typescript-eslint/no-use-before-define': ['error', { classes: false, functions: false, variables: true }],\n    '@typescript-eslint/no-useless-constructor': 'off',\n    '@typescript-eslint/no-useless-empty-export': 'error',\n    '@typescript-eslint/no-var-requires': 'off',\n    '@typescript-eslint/triple-slash-reference': 'off',\n    '@typescript-eslint/unified-signatures': 'off',\n}\n\nconst typeCheckedRules: Linter.RulesRecord = {\n    '@typescript-eslint/consistent-type-assertions': 'off',\n    '@typescript-eslint/consistent-type-definitions': 'off',\n    '@typescript-eslint/consistent-type-exports': 'error',\n    '@typescript-eslint/explicit-member-accessibility': 'error',\n    '@typescript-eslint/no-base-to-string': 'off',\n    '@typescript-eslint/no-confusing-void-expression': 'off',\n    '@typescript-eslint/no-deprecated': 'error',\n    '@typescript-eslint/no-dynamic-delete': 'off',\n    '@typescript-eslint/no-explicit-any': 'off',\n    '@typescript-eslint/no-floating-promises': 'off',\n    '@typescript-eslint/no-invalid-void-type': 'off',\n    '@typescript-eslint/no-misused-promises': 'off',\n    '@typescript-eslint/no-non-null-assertion': 'off',\n    '@typescript-eslint/no-redundant-type-constituents': 'off',\n    '@typescript-eslint/no-unnecessary-qualifier': 'error',\n    '@typescript-eslint/no-unnecessary-type-parameters': 'off',\n    '@typescript-eslint/no-unsafe-argument': 'off',\n    '@typescript-eslint/no-unsafe-assignment': 'off',\n    '@typescript-eslint/no-unsafe-call': 'off',\n    '@typescript-eslint/no-unsafe-member-access': 'off',\n    '@typescript-eslint/no-unsafe-return': 'off',\n    '@typescript-eslint/prefer-readonly': 'error',\n    '@typescript-eslint/prefer-regexp-exec': 'error',\n    '@typescript-eslint/require-array-sort-compare': ['error', { ignoreStringArrays: true }],\n    '@typescript-eslint/restrict-template-expressions': ['error', { allowNumber: true, allowBoolean: true, allowAny: true, allowNullish: true, allowRegExp: true, allowNever: true }],\n    '@typescript-eslint/use-unknown-in-catch-callback-variable': 'off',\n    '@typescript-eslint/unbound-method': 'off',\n    '@typescript-eslint/prefer-promise-reject-errors': 'off',\n}\n\nconst pluginConfigs = [tseslint.configs.eslintRecommended, tseslint.configs.strict, tseslint.configs.stylistic] as Linter.Config[][]\nconst pluginTypeCheckedConfigs = [tseslint.configs.strictTypeChecked, tseslint.configs.stylisticTypeChecked] as Linter.Config[][]\n\nexport interface TypescriptLanguageOptions {\n    tsconfigPath?: string | string[]\n    tsconfigRootDir?: string\n}\n\nconst languageOptions = ({ tsconfigPath, tsconfigRootDir }: TypescriptLanguageOptions) => ({\n    parserOptions: {\n        project: tsconfigPath,\n        tsconfigRootDir,\n        projectService: true,\n    },\n})\n\nconst declareFilesConfig: Linter.Config = {\n    files: ['**/*.d.ts'],\n    rules: {\n        'no-restricted-syntax': 'off',\n    },\n}\n\nconst jsFilesConfig: Linter.Config = {\n    files: ['**/*.js', '**/*.cjs'],\n    rules: {\n        '@typescript-eslint/no-require-imports': 'off',\n        '@typescript-eslint/no-var-requires': 'off',\n    },\n}\n\nexport interface TypescriptOptions {\n    componentExts?: string[]\n    tsconfig?: TypescriptLanguageOptions\n}\n\nexport function typescript({ componentExts = [], tsconfig = { tsconfigPath: 'tsconfig.json', tsconfigRootDir: process.cwd() } }: TypescriptOptions = {}): Promise<Linter.Config[]> {\n    const files = ['**/*.?([cm])[jt]s?(x)', ...componentExts.map((ext) => `**/*.${ext}`)]\n    const baseConfigs = [declareFilesConfig, jsFilesConfig]\n\n    baseConfigs.push({\n        files,\n        languageOptions: {\n            parserOptions: {\n                extraFileExtensions: componentExts.map((ext) => `.${ext}`),\n                sourceType: 'module',\n                warnOnUnsupportedTypeScriptVersion: false,\n            },\n        },\n    })\n\n    let configs: Promise<Linter.Config[]>\n\n    if (tsconfig) {\n        configs = concat(...pluginTypeCheckedConfigs, { languageOptions: languageOptions(tsconfig) }, { rules: { ...rules, ...typeCheckedRules } }, ...baseConfigs)\n    } else {\n        configs = concat(...pluginConfigs, { rules }, ...baseConfigs)\n    }\n\n    return configs.then((c) => c.map((config) => ({ ...config, files })))\n}\n", "import { concat } from 'eslint-flat-config-utils'\nimport plugin from 'eslint-plugin-unused-imports'\n\nexport const unusedImports = () => concat(\n    {\n        plugins: {\n            'unused-imports': plugin,\n        },\n        rules: {\n            'no-unused-vars': 'off',\n            '@typescript-eslint/no-unused-vars': 'off',\n            'unused-imports/no-unused-imports': 'error',\n            'unused-imports/no-unused-vars': ['error', { args: 'after-used', argsIgnorePattern: '^_', vars: 'all', varsIgnorePattern: '^_' }],\n        },\n    },\n    {\n        files: ['**/*.d.ts'],\n        rules: {\n            'unused-imports/no-unused-vars': 'off',\n        },\n    },\n)\n", "import { concat } from 'eslint-flat-config-utils'\nimport plugin from 'eslint-plugin-better-tailwindcss'\n\nexport const DEFAULT_TAILWIND_ENTRY_POINT = 'src/styles/main.css'\n\nexport const DEFAULT_TAILWIND_CONFIG = 'tailwind.config.ts'\n\nexport interface TailwindOptions {\n    entryPoint?: string\n    tailwindConfig?: string\n    attributes?: string[]\n    callees?: string[]\n    variables?: string[]\n    tags?: string[]\n}\n\nexport const tailwind = ({ entryPoint = DEFAULT_TAILWIND_ENTRY_POINT, tailwindConfig = DEFAULT_TAILWIND_CONFIG, ...options }: TailwindOptions = {}) => concat({\n    plugins: {\n        'better-tailwindcss': plugin,\n    },\n    settings: {\n        'better-tailwindcss': { entryPoint, tailwindConfig, ...options },\n    },\n    rules: {\n        ...plugin.configs['recommended-error'].rules,\n        'better-tailwindcss/multiline': 'off',\n        'better-tailwindcss/no-unregistered-classes': 'off',\n    },\n})\n", "import type { <PERSON><PERSON> } from 'eslint'\nimport { notNullish } from '@kdt310722/utils/common'\nimport typescriptParser from '@typescript-eslint/parser'\nimport { concat } from 'eslint-flat-config-utils'\nimport { mergeProcessors } from 'eslint-merge-processors'\nimport plugin from 'eslint-plugin-vue'\nimport pluginVueA11y from 'eslint-plugin-vuejs-accessibility'\nimport processorVueBlocks from 'eslint-processor-vue-blocks'\nimport vueParser from 'vue-eslint-parser'\nimport { stylistic } from '../base'\n\nexport async function getStylisticRules() {\n    const stylisticRules: Linter.RulesRecord = {}\n    const stylisticConfigs = await stylistic()\n\n    for (const config of stylisticConfigs) {\n        if (config.rules) {\n            for (const [ruleName, ruleOptions] of Object.entries(config.rules)) {\n                if (notNullish(ruleOptions)) {\n                    const name = ruleName.startsWith('@stylistic/') ? ruleName.replace('@stylistic/', '') : ruleName\n\n                    if (name in plugin.rules) {\n                        stylisticRules[`vue/${name}`] = ruleOptions\n                    }\n                }\n            }\n        }\n    }\n\n    return stylisticRules\n}\n\nconst processor = mergeProcessors([\n    plugin.processors['.vue'],\n    processorVueBlocks({ blocks: { styles: true, customBlocks: true } }),\n])\n\nexport const vue = async () => concat(\n    plugin.configs['flat/base'],\n    plugin.configs['flat/essential'],\n    plugin.configs['flat/strongly-recommended'],\n    plugin.configs['flat/recommended'],\n    pluginVueA11y.configs['flat/recommended'],\n    {\n        files: ['**/*.vue'],\n        processor,\n        languageOptions: {\n            parser: vueParser,\n            parserOptions: {\n                parser: typescriptParser,\n                extraFileExtensions: ['.vue'],\n                sourceType: 'module',\n                ecmaFeatures: {\n                    jsx: true,\n                },\n            },\n        },\n        rules: {\n            ...(await getStylisticRules()),\n            '@stylistic/indent': 'off',\n            'vue/block-lang': ['error', { script: { lang: 'ts' } }],\n            'vue/block-order': ['error', { order: ['script', 'template', 'style'] }],\n            'vue/block-tag-newline': ['error', { maxEmptyLines: 1, multiline: 'always', singleline: 'consistent' }],\n            'vue/component-api-style': ['error', ['script-setup', 'composition']],\n            'vue/component-name-in-template-casing': 'error',\n            'vue/component-options-name-casing': 'error',\n            'vue/custom-event-name-casing': ['error', 'kebab-case'],\n            'vue/define-emits-declaration': 'error',\n            'vue/define-macros-order': ['error', { order: ['defineOptions', 'defineProps', 'defineEmits', 'defineSlots'] }],\n            'vue/define-props-declaration': 'error',\n            'vue/html-button-has-type': 'error',\n            'vue/html-closing-bracket-newline': ['error', { multiline: 'always', singleline: 'never' }],\n            'vue/html-comment-content-newline': 'error',\n            'vue/html-comment-content-spacing': 'error',\n            'vue/html-comment-indent': ['error', 4],\n            'vue/html-indent': ['error', 4],\n            'vue/html-quotes': ['error', 'double'],\n            'vue/match-component-file-name': ['error', { extensions: ['.jsx', '.tsx', '.vue'] }],\n            'vue/match-component-import-name': 'error',\n            'vue/max-attributes-per-line': 'off',\n            'vue/multi-word-component-names': 'off',\n            'vue/next-tick-style': 'error',\n            'vue/no-deprecated-model-definition': ['error', { allowVue3Compat: true }],\n            'vue/no-duplicate-attr-inheritance': 'off',\n            'vue/no-empty-component-block': 'error',\n            'vue/no-extra-parens': ['error', 'functions'],\n            'vue/no-multiple-objects-in-class': 'error',\n            'vue/no-potential-component-option-typo': ['error', { presets: ['all'] }],\n            'vue/no-ref-object-reactivity-loss': 'error',\n            'vue/no-required-prop-with-default': ['error', { autofix: true }],\n            'vue/no-restricted-v-bind': ['error', '/^v-/'],\n            'vue/no-setup-props-reactivity-loss': 'off',\n            'vue/no-this-in-before-route-enter': 'error',\n            'vue/no-unsupported-features': ['error', { version: '>=3.4.0' }],\n            'vue/no-unused-refs': 'error',\n            'vue/no-unused-vars': 'error',\n            'vue/no-useless-mustaches': 'error',\n            'vue/no-useless-v-bind': 'error',\n            'vue/no-v-html': 'off',\n            'vue/padding-line-between-blocks': 'error',\n            'vue/padding-lines-in-component-definition': 'error',\n            'vue/prefer-define-options': 'error',\n            'vue/prefer-prop-type-boolean-first': 'error',\n            'vue/prefer-separate-static-class': 'error',\n            'vue/prefer-true-attribute-shorthand': 'error',\n            'vue/require-default-prop': 'off',\n            'vue/require-direct-export': 'error',\n            'vue/require-emit-validator': 'error',\n            'vue/require-macro-variable-name': 'error',\n            'vue/require-name-property': 'error',\n            'vue/require-typed-object-prop': 'error',\n            'vue/require-typed-ref': 'error',\n            'vue/script-indent': ['error', 4, { baseIndent: 1, switchCase: 1 }],\n            'vue/singleline-html-element-content-newline': 'off',\n            'vue/v-for-delimiter-style': 'error',\n            'vue/valid-define-options': 'error',\n            'vue/valid-v-bind': 'off',\n        },\n    },\n)\n", "import type { Lin<PERSON> } from 'eslint'\nimport type { Options } from 'prettier'\nimport { resolveNestedOptions } from '@kdt310722/utils/object'\nimport { concat } from 'eslint-flat-config-utils'\nimport plugin from 'eslint-plugin-format'\n\nconst defaultPrettierOptions: Options = {\n    arrowParens: 'always',\n    bracketSameLine: false,\n    bracketSpacing: true,\n    endOfLine: 'lf',\n    htmlWhitespaceSensitivity: 'ignore',\n    jsxSingleQuote: false,\n    printWidth: Number.POSITIVE_INFINITY,\n    quoteProps: 'consistent',\n    semi: false,\n    singleAttributePerLine: false,\n    singleQuote: true,\n    tabWidth: 4,\n    trailingComma: 'es5',\n    useTabs: false,\n    vueIndentScriptAndStyle: true,\n}\n\nconst getEslintConfig = (files: string[], prettierOptions: Options = {}): Linter.Config => ({\n    files,\n    languageOptions: {\n        parser: plugin.parserPlain,\n    },\n    plugins: {\n        format: plugin,\n    },\n    rules: {\n        'format/prettier': ['error', { ...defaultPrettierOptions, ...prettierOptions }],\n    },\n})\n\nexport interface PrettierTailwindOptions {\n    enabled?: boolean\n    tailwindStylesheet?: string\n    tailwindConfig?: string\n    tailwindAttributes?: string[]\n    tailwindFunctions?: string[]\n    tailwindPreserveWhitespace?: boolean\n    tailwindPreserveDuplicates?: boolean\n}\n\nexport interface PrettierGetSupportedFilesOptions {\n    tailwind?: PrettierTailwindOptions | boolean\n}\n\nconst getSupportedFiles = ({ tailwind = false }: PrettierGetSupportedFilesOptions = {}): Array<[files: string[], options: Options]> => {\n    const tailwindOptions = resolveNestedOptions(tailwind) || { enabled: false }\n\n    return [\n        [['**/*.css'], { parser: 'css' }],\n        [['**/*.{p,post}css'], { parser: 'css' }],\n        [['**/*.scss'], { parser: 'scss' }],\n        [['**/*.less'], { parser: 'less' }],\n        [['**/*.md'], { parser: 'markdown' }],\n        [['**/*.htm?(l)'], { parser: 'html', ...(tailwindOptions.enabled ? { plugins: ['eslint-plugin-tailwindcss'], ...tailwindOptions } : {}) }],\n        [['**/*.y?(a)ml'], { parser: 'yaml' }],\n    ]\n}\n\nexport type FormatOptions = PrettierGetSupportedFilesOptions\n\nexport const format = (options: FormatOptions = {}) => concat(...getSupportedFiles(options).map(([files, prettierOptions]) => {\n    return getEslintConfig(files, prettierOptions)\n}))\n", "import plugin from 'eslint-config-flat-gitignore'\n\nexport const gitignore = () => plugin({\n    strict: false,\n})\n", "import type { Lin<PERSON> } from 'eslint'\nimport { concat } from 'eslint-flat-config-utils'\nimport plugin from 'eslint-plugin-perfectionist'\n\nexport const perfectionist = () => concat(<Linter.Config>{\n    plugins: {\n        perfectionist: plugin,\n    },\n    rules: {\n        'perfectionist/sort-exports': ['error', { order: 'asc', type: 'natural' }],\n        'perfectionist/sort-imports': ['error', { groups: ['type', ['parent-type', 'sibling-type', 'index-type', 'internal-type'], 'builtin', 'external', 'internal', ['parent', 'sibling', 'index'], 'side-effect', 'object', 'unknown'], newlinesBetween: 'ignore', order: 'asc', type: 'natural' }],\n        'perfectionist/sort-named-exports': ['error', { order: 'asc', type: 'natural' }],\n        'perfectionist/sort-named-imports': ['error', { order: 'asc', type: 'natural' }],\n        'import-x/order': 'off',\n        'sort-imports': 'off',\n    },\n})\n", "import { concat } from 'eslint-flat-config-utils'\nimport plugin from 'eslint-plugin-sonarjs'\n\nexport const sonar = () => concat(plugin.configs.recommended, {\n    rules: {\n        'sonarjs/arguments-order': 'off',\n        'sonarjs/assertions-in-tests': 'off',\n        'sonarjs/cognitive-complexity': 'off',\n        'sonarjs/function-return-type': 'off',\n        'sonarjs/hashing': 'off',\n        'sonarjs/no-async-constructor': 'off',\n        'sonarjs/no-clear-text-protocols': 'off',\n        'sonarjs/no-duplicate-string': 'off',\n        'sonarjs/no-hardcoded-secrets': 'off',\n        'sonarjs/no-invariant-returns': 'off',\n        'sonarjs/no-nested-assignment': 'off',\n        'sonarjs/no-nested-conditional': 'off',\n        'sonarjs/no-nested-functions': 'off',\n        'sonarjs/no-nested-template-literals': 'off',\n        'sonarjs/no-os-command-from-path': 'off',\n        'sonarjs/pseudo-random': 'off',\n        'sonarjs/redundant-type-aliases': 'off',\n        'sonarjs/todo-tag': 'off',\n        'sonarjs/updated-loop-counter': 'off',\n        'sonarjs/no-selector-parameter': 'off',\n        'sonarjs/no-dead-store': 'off',\n    },\n})\n", "import { concat } from 'eslint-flat-config-utils'\nimport plugin from 'eslint-plugin-unicorn'\n\nexport const unicorn = () => concat(plugin.configs.recommended, {\n    rules: {\n        'unicorn/consistent-destructuring': 'off',\n        'unicorn/consistent-function-scoping': ['error', { checkArrowFunctions: false }],\n        'unicorn/filename-case': ['error', { cases: { kebabCase: true, pascalCase: true }, ignore: [String.raw`.*\\.md$`] }],\n        'unicorn/import-style': 'off',\n        'unicorn/new-for-builtins': 'off',\n        'unicorn/no-array-callback-reference': 'off',\n        'unicorn/no-array-method-this-argument': 'off',\n        'unicorn/no-array-push-push': 'off',\n        'unicorn/no-array-reduce': 'off',\n        'unicorn/no-await-expression-member': 'off',\n        'unicorn/no-empty-file': 'off',\n        'unicorn/no-null': 'off',\n        'unicorn/no-process-exit': 'off',\n        'unicorn/prefer-event-target': 'off',\n        'unicorn/prefer-math-min-max': 'off',\n        'unicorn/prefer-module': 'off',\n        'unicorn/prefer-ternary': 'off',\n        'unicorn/prefer-top-level-await': 'off',\n        'unicorn/prevent-abbreviations': 'off',\n        'unicorn/require-number-to-fixed-digits-argument': 'off',\n        'unicorn/no-array-for-each': 'off',\n        'unicorn/switch-case-braces': ['error', 'avoid'],\n    },\n})\n", "import type { <PERSON><PERSON> } from 'eslint'\nimport { resolveNestedOptions } from '@kdt310722/utils/object'\nimport { composer } from 'eslint-flat-config-utils'\nimport { antfu, comments, format, type FormatOptions, gitignore, ignores, importX, javascript, jsonc, kdt, node, perfectionist, promise, regexp, sonar, stylistic, tailwind, type TailwindOptions, typescript, type TypescriptOptions, unicorn, unusedImports, vue } from './configs'\n\nexport interface DefineConfigOptions {\n    tailwind?: TailwindOptions | boolean\n    typescript?: TypescriptOptions\n    format?: FormatOptions\n    vue?: boolean\n    config?: Linter.Config\n    configs?: Linter.Config[]\n}\n\nexport function defineConfig({ tailwind: tailwind_ = true, typescript: typescriptOptions = {}, format: formatOptions, vue: isVueEnabled, config, configs: configs_ }: DefineConfigOptions = {}) {\n    typescriptOptions.componentExts ??= []\n\n    if (isVueEnabled) {\n        typescriptOptions.componentExts.push('vue')\n    }\n\n    const configs = composer(ignores(), gitignore(), javascript(), typescript(typescriptOptions), stylistic(), comments(), importX(), jsonc(), node(), promise(), regexp(), unusedImports(), antfu(), kdt(), format(formatOptions), perfectionist(), sonar(), unicorn())\n\n    if (isVueEnabled) {\n        configs.append(vue())\n    }\n\n    const tailwindOptions = resolveNestedOptions(tailwind_)\n\n    if (tailwindOptions !== false) {\n        configs.append(tailwind(tailwindOptions))\n    }\n\n    if (config) {\n        configs.append(config)\n    }\n\n    if (configs_) {\n        configs.append(configs_)\n    }\n\n    return configs\n}\n"], "mappings": ";AAAA,SAAS,cAAc;AACvB,OAAO,YAAY;AAEZ,IAAM,QAAQ,MAAM,OAAO;AAAA,EAC9B;AAAA,IACI,SAAS;AAAA,MACL,OAAO;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACH,oBAAoB;AAAA,MACpB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,wCAAwC;AAAA,IAC5C;AAAA,EACJ;AAAA,EACA;AAAA,IACI,OAAO,CAAC,aAAa;AAAA,IACrB,OAAO;AAAA,MACH,wBAAwB;AAAA,MACxB,wCAAwC;AAAA,IAC5C;AAAA,EACJ;AACJ,CAAC;;;ACtBD,OAAO,aAAa;AACpB,SAAS,UAAAA,eAAc;AAEhB,IAAM,WAAW,MAAMA,QAAO,QAAQ,aAAa;AAAA,EACtD,OAAO,CAAC,WAAW;AAAA,EACnB,OAAO;AAAA,IACH,0DAA0D;AAAA,EAC9D;AACJ,CAAC;;;ACRD,SAAS,UAAAC,eAAc;AAEhB,IAAM,UAAU,MAAMA,QAAO;AAAA,EAChC,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ,CAAC;;;ACvDD,SAAS,UAAAC,eAAc;AACvB,SAAS,WAAWC,eAAc;AAElC,IAAM,gBAAgB,CAACA,QAAO,YAAY,aAAaA,QAAO,YAAY,UAAU;AAE7E,IAAM,UAAU,MAAMD;AAAA,EACzB,GAAG;AAAA,EACH;AAAA,IACI,OAAO;AAAA,MACH,kBAAkB;AAAA,MAClB,iCAAiC,CAAC,SAAS,EAAE,kBAAkB,MAAM,OAAO,EAAE,CAAC;AAAA,MAC/E,0BAA0B;AAAA,MAC1B,kCAAkC;AAAA,MAClC,+BAA+B;AAAA,MAC/B,gCAAgC;AAAA,MAChC,uCAAuC;AAAA,MACvC,6BAA6B;AAAA,MAC7B,2BAA2B;AAAA,MAC3B,0BAA0B;AAAA,MAC1B,8BAA8B;AAAA,MAC9B,qCAAqC;AAAA,MACrC,qCAAqC;AAAA,MACrC,kBAAkB;AAAA,IACtB;AAAA,EACJ;AAAA,EACA;AAAA,IACI,OAAO,CAAC,WAAW;AAAA,IACnB,OAAO;AAAA,MACH,0BAA0B;AAAA,IAC9B;AAAA,EACJ;AACJ;;;AC/BA,OAAO,QAAQ;AACf,SAAS,UAAAE,eAAc;AACvB,OAAO,aAAa;AAEpB,IAAM,QAA4B;AAAA,EAC9B,kBAAkB,CAAC,SAAS,EAAE,wBAAwB,MAAM,eAAe,KAAK,CAAC;AAAA,EACjF,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,SAAS,CAAC,SAAS,KAAK;AAAA,EACxB,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,UAAU,CAAC,SAAS,OAAO;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,gCAAgC;AAAA,EAChC,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,wBAAwB;AAAA,EACxB,aAAa;AAAA,EACb,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,QAAQ,OAAO,EAAE,CAAC;AAAA,EACpD,kBAAkB,CAAC,SAAS,EAAE,aAAa,KAAK,CAAC;AAAA,EACjD,YAAY,CAAC,SAAS,EAAE,iBAAiB,KAAK,CAAC;AAAA,EAC/C,WAAW;AAAA,EACX,oBAAoB;AAAA,EACpB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,kBAAkB,CAAC,SAAS,EAAE,gBAAgB,MAAM,gBAAgB,OAAO,yBAAyB,CAAC;AAAA,EACrG,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,aAAa,CAAC,SAAS,EAAE,WAAW,OAAO,aAAa,MAAM,CAAC;AAAA,EAC/D,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,gBAAgB,CAAC,SAAS,EAAE,gBAAgB,MAAM,CAAC;AAAA,EACnD,yBAAyB,CAAC,SAAS,EAAE,SAAS,6BAA6B,MAAM,SAAS,GAAG,EAAE,SAAS,6BAA6B,MAAM,OAAO,CAAC;AAAA,EACnJ,4BAA4B,CAAC,SAAS,EAAE,SAAS,mEAAmE,UAAU,YAAY,GAAG,EAAE,SAAS,wCAAwC,UAAU,mBAAmB,GAAG,EAAE,SAAS,wCAAwC,UAAU,mBAAmB,GAAG,EAAE,SAAS,kDAAkD,UAAU,mBAAmB,GAAG,EAAE,SAAS,kDAAkD,UAAU,mBAAmB,CAAC;AAAA,EAC3e,wBAAwB,CAAC,SAAS,qBAAqB,oBAAoB,iBAAiB,iCAAiC,oBAAoB;AAAA,EACjJ,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,+BAA+B;AAAA,EAC/B,oBAAoB;AAAA,EACpB,iBAAiB;AAAA,EACjB,gCAAgC;AAAA,EAChC,uBAAuB,CAAC,SAAS,EAAE,mBAAmB,MAAM,CAAC;AAAA,EAC7D,uBAAuB;AAAA,EACvB,yBAAyB,CAAC,SAAS,EAAE,mBAAmB,MAAM,sBAAsB,MAAM,cAAc,KAAK,CAAC;AAAA,EAC9G,kBAAkB,CAAC,SAAS,EAAE,MAAM,QAAQ,cAAc,QAAQ,oBAAoB,MAAM,MAAM,MAAM,CAAC;AAAA,EACzG,wBAAwB,CAAC,SAAS,EAAE,SAAS,OAAO,WAAW,OAAO,WAAW,KAAK,CAAC;AAAA,EACvF,mBAAmB;AAAA,EACnB,2BAA2B,CAAC,SAAS,EAAE,wBAAwB,KAAK,CAAC;AAAA,EACrE,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,UAAU;AAAA,EACV,oBAAoB,CAAC,SAAS,cAAc,EAAE,aAAa,KAAK,CAAC;AAAA,EACjE,WAAW,CAAC,SAAS,EAAE,aAAa,QAAQ,CAAC;AAAA,EAC7C,uBAAuB;AAAA,EACvB,yBAAyB,CAAC,SAAS,EAAE,qBAAqB,OAAO,kBAAkB,KAAK,CAAC;AAAA,EACzF,gBAAgB,CAAC,SAAS,EAAE,eAAe,OAAO,wBAAwB,KAAK,CAAC;AAAA,EAChF,kCAAkC;AAAA,EAClC,gCAAgC,CAAC,SAAS,EAAE,kBAAkB,KAAK,CAAC;AAAA,EACpE,yBAAyB,CAAC,SAAS,EAAE,2BAA2B,KAAK,CAAC;AAAA,EACtE,sBAAsB;AAAA,EACtB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,gBAAgB,CAAC,SAAS,EAAE,sBAAsB,OAAO,YAAY,OAAO,uBAAuB,MAAM,kBAAkB,OAAO,uBAAuB,CAAC,QAAQ,OAAO,YAAY,QAAQ,EAAE,CAAC;AAAA,EAChM,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,QAAQ;AACZ;AAEA,IAAM,SAAwB;AAAA,EAC1B,iBAAiB;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,SAAS;AAAA,MACL,GAAG,QAAQ;AAAA,MACX,GAAG,QAAQ;AAAA,MACX,GAAG,QAAQ;AAAA,MACX,UAAU;AAAA,MACV,WAAW;AAAA,MACX,QAAQ;AAAA,IACZ;AAAA,IACA,eAAe;AAAA,MACX,cAAc,EAAE,KAAK,KAAK;AAAA,MAC1B,aAAa;AAAA,MACb,YAAY;AAAA,IAChB;AAAA,EACJ;AAAA,EACA,eAAe;AAAA,IACX,+BAA+B;AAAA,EACnC;AAAA,EACA;AACJ;AAEA,IAAM,yBAAwC;AAAA,EAC1C,OAAO,CAAC,+BAA+B;AAAA,EACvC,OAAO;AAAA,IACH,cAAc;AAAA,EAClB;AACJ;AAEA,IAAM,qBAAoC;AAAA,EACtC,OAAO,CAAC,mCAAmC;AAAA,EAC3C,OAAO;AAAA,IACH,yBAAyB;AAAA,EAC7B;AACJ;AAEO,IAAM,aAAa,MAAMA,QAAO,GAAG,QAAQ,aAA8B,QAAQ,wBAAwB,kBAAkB;;;ACnHlI,SAAS,UAAAC,eAAc;AACvB,OAAOC,aAAY;AAEZ,IAAM,QAAQ,MAAMD;AAAA,EACvBC,QAAO,QAAQ,4BAA4B;AAAA,EAC3CA,QAAO,QAAQ,6BAA6B;AAAA,EAC5CA,QAAO,QAAQ,6BAA6B;AAAA,EAC5C;AAAA,IACI,OAAO;AAAA,MACH,sBAAsB,CAAC,SAAS,OAAO;AAAA,MACvC,gBAAgB,CAAC,SAAS,QAAQ;AAAA,IACtC;AAAA,EACJ;AAAA,EACA;AAAA,IACI,OAAO,CAAC,iBAAiB;AAAA,IACzB,OAAO;AAAA,MACH,2BAA2B,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,MAAM,GAAG,aAAa,UAAU,CAAC;AAAA,MACvF,mBAAmB,CAAC,SAAS,EAAE,OAAO,CAAC,aAAa,QAAQ,eAAe,QAAQ,WAAW,WAAW,kBAAkB,eAAe,UAAU,WAAW,OAAO,eAAe,WAAW,YAAY,cAAc,QAAQ,YAAY,MAAM,OAAO,cAAc,eAAe,WAAW,QAAQ,UAAU,SAAS,YAAY,SAAS,iBAAiB,OAAO,QAAQ,SAAS,WAAW,cAAc,SAAS,oBAAoB,eAAe,WAAW,oBAAoB,wBAAwB,gBAAgB,wBAAwB,mBAAmB,uBAAuB,oBAAoB,QAAQ,iBAAiB,UAAU,aAAa,eAAe,SAAS,oBAAoB,eAAe,gBAAgB,cAAc,GAAG,aAAa,KAAK,GAAG,EAAE,OAAO,EAAE,MAAM,MAAM,GAAG,aAAa,yDAAyD,GAAG,EAAE,OAAO,EAAE,MAAM,MAAM,GAAG,aAAa,6CAA6C,GAAG,EAAE,OAAO,CAAC,SAAS,UAAU,WAAW,SAAS,GAAG,aAAa,cAAc,CAAC;AAAA,IAC7gC;AAAA,EACJ;AAAA,EACA;AAAA,IACI,OAAO,CAAC,oBAAoB,oBAAoB;AAAA,IAChD,OAAO;AAAA,MACH,mBAAmB,CAAC,SAAS,EAAE,OAAO,CAAC,WAAW,mBAAmB,iBAAiB,gBAAgB,gBAAgB,cAAc,SAAS,WAAW,WAAW,SAAS,GAAG,aAAa,KAAK,GAAG,EAAE,OAAO,CAAC,eAAe,aAAa,mBAAmB,2CAA2C,4BAA4B,gCAAgC,UAAU,OAAO,cAAc,sBAAsB,mBAAmB,OAAO,mBAAmB,SAAS,kBAAkB,2BAA2B,yBAAyB,0BAA0B,WAAW,WAAW,YAAY,oBAAoB,UAAU,oBAAoB,kBAAkB,aAAa,SAAS,qBAAqB,6BAA6B,6BAA6B,aAAa,SAAS,4BAA4B,8BAA8B,wBAAwB,sBAAsB,WAAW,WAAW,wBAAwB,UAAU,uBAAuB,uBAAuB,oBAAoB,gCAAgC,wBAAwB,qBAAqB,gBAAgB,8BAA8B,8BAA8B,iBAAiB,sBAAsB,qBAAqB,kBAAkB,sCAAsC,4BAA4B,kBAAkB,sBAAsB,8BAA8B,eAAe,kBAAkB,kBAAkB,sBAAsB,WAAW,uBAAuB,iBAAiB,0BAA0B,mBAAmB,iBAAiB,WAAW,WAAW,UAAU,iBAAiB,iBAAiB,UAAU,WAAW,sBAAsB,wBAAwB,kBAAkB,aAAa,cAAc,iBAAiB,gCAAgC,mBAAmB,oCAAoC,mBAAmB,oBAAoB,wBAAwB,uBAAuB,cAAc,GAAG,aAAa,oBAAoB,CAAC;AAAA,IACl8D;AAAA,EACJ;AACJ;;;AC3BA,SAAS,UAAAC,eAAc;;;ACGnB,cAAW;;;ACHf,SAAS,kBAAAC,iBAAgB,gBAAgB;;;ACCzC,SAAS,sBAAsB;AAExB,SAAS,iBAAiBC,OAAkE;AAC/F,SAAOA,OAAM,SAAS,eAAe;AACzC;;;ACJA,SAAS,mBAAsC;AAQxC,SAAS,WAA2D,MAAkB;AACzF,SAAO,YAAY,YAAY,IAAW;AAC9C;;;AFRO,IAAM,wBAAwB,WAAW;AAAA,EAC5C,MAAM;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,IACT,QAAQ,CAAC;AAAA,IACT,UAAU;AAAA,MACN,mBAAmB;AAAA,IACvB;AAAA,EACJ;AAAA,EACA,gBAAgB,CAAC;AAAA,EACjB,OAAO,EAAE,YAAY,OAAO,GAAG;AAC3B,WAAO;AAAA,MACH,CAACC,gBAAe,uBAAuB,EAAEC,OAAM;AAC3C,cAAM,iBAAiB,WAAW,kBAAkBA,MAAK,IAAI;AAE7D,YAAI,eAAe,WAAW,KAAK,iBAAiBA,MAAK,IAAI,KAAKA,MAAK,KAAK,KAAK,WAAW,GAAG;AAC3F,gBAAM,cAAc,WAAW,cAAcA,MAAK,IAAI;AACtD,gBAAM,eAAe,WAAW,aAAaA,MAAK,IAAI;AAEtD,cAAI,eAAe,gBAAgB,CAAC,SAAS,kBAAkB,aAAa,YAAY,GAAG;AACvF,mBAAO,EAAE,MAAAA,OAAM,KAAKA,MAAK,KAAK,KAAK,WAAW,qBAAqB,KAAK,CAAC,UAAU,MAAM,iBAAiBA,MAAK,KAAK,OAAO,IAAI,EAAE,CAAC;AAAA,UACtI;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ,CAAC;;;AG7BD,SAAS,kBAAAC,uBAAsB;AAG/B,IAAM,mBAAmB;AAElB,IAAM,mBAAmB,WAAW;AAAA,EACvC,MAAM;AAAA,IACF,MAAM;AAAA,IACN,SAAS;AAAA,IACT,QAAQ,CAAC;AAAA,IACT,UAAU,EAAE,qBAAqB,4CAA4C;AAAA,EACjF;AAAA,EACA,gBAAgB,CAAC;AAAA,EACjB,OAAO,EAAE,YAAY,OAAO,GAAG;AAC3B,WAAO;AAAA,MACH,CAACC,gBAAe,iBAAiB,EAAEC,OAAM;AACrC,cAAM,SAAS,WAAW,QAAQA,KAAI;AAEtC,YAAI,CAAC,iBAAiB,KAAK,MAAM,GAAG;AAChC;AAAA,QACJ;AAEA,eAAO,EAAE,MAAAA,OAAM,WAAW,uBAAuB,KAAK,CAAC,UAAU,MAAM,YAAYA,OAAM,OAAO,QAAQ,kBAAkB,EAAE,CAAC,EAAE,CAAC;AAAA,MACpI;AAAA,IACJ;AAAA,EACJ;AACJ,CAAC;;;AC1BD,OAAO,eAAe;AACtB,SAAS,YAAAC,iBAAgB;AAyBzB,IAAM,WAAW,UAAU,MAAM,sBAAsB;AAEhD,IAAM,qBAAqB,WAAgC;AAAA,EAC9D,MAAM,SAAS;AAAA,EACf,gBAAgB;AAAA,IACZ;AAAA,MACI,kBAAkB,EAAE,WAAW,KAAK;AAAA,MACpC,eAAe,EAAE,WAAW,MAAM,YAAY,KAAK;AAAA,MACnD,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,IACvB;AAAA,EACJ;AAAA,EACA,OAAO,SAAS,CAAC,OAAO,GAAG;AACvB,aAAS,SAAS,MAAgB;AAC9B,YAAM,sBAAsB,OAAO,OAAO,SAAS;AAAA,QAC/C,SAAS,EAAE,UAAU,OAAO,cAAc,OAAO,OAAO,CAAC,IAAI,EAAE;AAAA,MACnE,CAAC;AAED,aAAO,SAAS,OAAO,qBAAqB,CAAC,IAAI,CAAC;AAAA,IACtD;AAEA,aAAS,mBAAmB;AACxB,UAAI,CAAC,WAAW,OAAO,YAAY,UAAU;AACzC,eAAO;AAAA,MACX;AAEA,UAAI,eAAe,WAAW,QAAQ,WAAW;AAC7C,eAAO,QAAQ,iBAAiB,OAAO;AAAA,MAC3C;AAEA,UAAI,EAAE,sBAAsB,YAAY,OAAO,QAAQ,qBAAqB,UAAU;AAClF,eAAO;AAAA,MACX;AAEA,UAAI,CAAC,QAAQ,kBAAkB,WAAW;AACtC,eAAO;AAAA,MACX;AAEA,aAAO,QAAQ,iBAAiB,iBAAiB,OAAO;AAAA,IAC5D;AAEA,UAAMC,SAAQ,SAAS,OAAO;AAC9B,UAAM,gBAAgB,iBAAiB;AAEvC,WAAO;AAAA,MACH,GAAGA;AAAA,MACH,iBAAiBC,OAAM;AACnB,YAAI,kBAAkB,SAASA,MAAK,WAAW,UAAU,eAAe;AACpE,iBAAOD,OAAM,mBAAmBC,KAAI;AAAA,QACxC;AAEA,cAAM,SAAS,QAAQ;AACvB,cAAM,YAAY,OAAO,cAAcA,OAAM,CAAC,UAAU,MAAM,UAAU,GAAG;AAC3E,cAAM,aAAa,OAAO,aAAaA,OAAM,CAAC,UAAU,MAAM,UAAU,GAAG;AAE3E,YAAI,CAAC,aAAa,CAAC,YAAY;AAC3B,iBAAOD,OAAM,mBAAmBC,KAAI;AAAA,QACxC;AAEA,YAAI,QAAQ,OAAO,cAAc,WAAW,EAAE,iBAAiB,KAAK,CAAC;AACrE,YAAI,OAAO,OAAO,eAAe,YAAY,EAAE,iBAAiB,KAAK,CAAC;AAEtE,YAAI,CAAC,SAAS,CAAC,MAAM;AACjB,iBAAOD,OAAM,mBAAmBC,KAAI;AAAA,QACxC;AAEA,YAAI,EAAEA,MAAK,WAAW,SAAS,KAAK,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI,IAAI,OAAO;AAC7E,kBAAQ,OAAO,cAAc,SAAS;AACtC,iBAAO,OAAO,eAAe,UAAU;AAEvC,cAAI,CAAC,SAAS,CAAC,MAAM;AACjB,mBAAOD,OAAM,mBAAmBC,KAAI;AAAA,UACxC;AAEA,gBAAM,uCAAuC,CAACC,UAAS,kBAAkB,WAAW,KAAK;AACzF,gBAAM,uCAAuC,CAACA,UAAS,kBAAkB,MAAM,UAAU;AAEzF,cAAI,wCAAwC,sCAAsC;AAC9E,mBAAO,SAAS,QAAQ,EAAE,mBAAmBD,KAAI;AAAA,UACrD;AAAA,QACJ;AAEA,eAAOD,OAAM,mBAAmBC,KAAI;AAAA,MACxC;AAAA,IACJ;AAAA,EACJ;AACJ,CAAC;;;AC5GD,IAAME,UAAS;AAAA,EACX,MAAM;AAAA,IACF,MAAM;AAAA,IACN;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH,4BAA4B;AAAA,IAC5B,sBAAsB;AAAA,IACtB,wBAAwB;AAAA,EAC5B;AACJ;AAEA,IAAO,cAAQA;;;APbR,IAAM,MAAM,MAAMC,QAAO;AAAA,EAC5B,SAAS;AAAA,IACL,KAAK;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACH,gCAAgC;AAAA,IAChC,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,mCAAmC;AAAA,EACvC;AACJ,CAAC;;;AQbD,SAAS,UAAAC,eAAc;AACvB,OAAOC,aAAY;AAEZ,IAAM,OAAO,MAAMD,QAAOC,QAAO,QAAQ,kBAAkB,GAAG;AAAA,EACjE,OAAO;AAAA,IACH,yBAAyB,CAAC,SAAS,eAAe;AAAA,IAClD,cAAc;AAAA,IACd,uBAAuB;AAAA,IACvB,wBAAwB;AAAA,IACxB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,2BAA2B;AAAA,IAC3B,4BAA4B;AAAA,IAC5B,2CAA2C;AAAA,IAC3C,aAAa;AAAA,EACjB;AACJ,CAAC;;;ACjBD,SAAS,UAAAC,eAAc;AACvB,OAAOC,aAAY;AAEZ,IAAM,UAAU,MAAMD,QAAOC,QAAO,QAAQ,kBAAkB,GAAG;AAAA,EACpE,OAAO;AAAA,IACH,yBAAyB;AAAA,IACzB,2BAA2B;AAAA,IAC3B,gCAAgC;AAAA,IAChC,sBAAsB;AAAA,IACtB,kCAAkC;AAAA,EACtC;AACJ,CAAC;;;ACXD,SAAS,UAAAC,gBAAc;AACvB,YAAYC,aAAY;AAEjB,IAAM,SAAS,MAAMD,SAAc,gBAAQ,kBAAkB,CAAC;;;ACFrE,OAAO,qBAAqB;AAC5B,SAAS,UAAAE,gBAAc;AAEvB,IAAM,oBAAoB;AAAA,EACtB,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,OAAO;AAAA,EACP,UAAU;AAAA,EACV,QAAQ;AACZ;AAEA,IAAM,2BAA2B;AAAA,EAC7B,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,mBAAmB;AACvB;AAEA,IAAM,qCAAqC;AAAA,EACvC,EAAE,WAAW,SAAS,MAAM,CAAC,SAAS,SAAS,GAAG,MAAM,IAAI;AAAA,EAC5D,EAAE,WAAW,SAAS,MAAM,KAAK,MAAM,CAAC,SAAS,QAAQ,SAAS,EAAE;AAAA,EACpE,EAAE,WAAW,SAAS,MAAM,QAAQ,MAAM,SAAS;AAAA,EACnD,EAAE,WAAW,UAAU,MAAM,aAAa,MAAM,IAAI;AAAA,EACpD,EAAE,WAAW,UAAU,MAAM,KAAK,MAAM,YAAY;AAAA,EACpD,EAAE,WAAW,UAAU,MAAM,SAAS,MAAM,IAAI;AAAA,EAChD,EAAE,WAAW,UAAU,MAAM,KAAK,MAAM,QAAQ;AAAA,EAChD,EAAE,WAAW,UAAU,MAAM,KAAK,MAAM,YAAY;AAAA,EACpD,EAAE,WAAW,UAAU,MAAM,KAAK,MAAM,CAAC,MAAM,OAAO,OAAO,EAAE;AAAA,EAC/D,EAAE,WAAW,UAAU,MAAM,CAAC,MAAM,OAAO,OAAO,GAAG,MAAM,IAAI;AAAA,EAC/D,EAAE,WAAW,UAAU,MAAM,KAAK,MAAM,WAAW;AAAA,EACnD,EAAE,WAAW,UAAU,MAAM,YAAY,MAAM,YAAY;AAAA,EAC3D,EAAE,WAAW,UAAU,MAAM,KAAK,MAAM,KAAK;AAAA,EAC7C,EAAE,WAAW,UAAU,MAAM,MAAM,MAAM,IAAI;AAAA,EAC7C,EAAE,WAAW,UAAU,MAAM,KAAK,MAAM,CAAC,wBAAwB,sBAAsB,EAAE;AAAA,EACzF,EAAE,WAAW,UAAU,MAAM,CAAC,wBAAwB,sBAAsB,GAAG,MAAM,IAAI;AAAA,EACzF,EAAE,WAAW,UAAU,MAAM,KAAK,MAAM,CAAC,mBAAmB,iBAAiB,eAAe,EAAE;AAAA,EAC9F,EAAE,WAAW,UAAU,MAAM,CAAC,mBAAmB,iBAAiB,eAAe,GAAG,MAAM,IAAI;AAAA,EAC9F,EAAE,WAAW,UAAU,MAAM,UAAU,MAAM,IAAI;AAAA,EACjD,EAAE,WAAW,UAAU,MAAM,KAAK,MAAM,SAAS;AAAA,EACjD,EAAE,WAAW,UAAU,MAAM,UAAU,MAAM,IAAI;AAAA,EACjD,EAAE,WAAW,UAAU,MAAM,KAAK,MAAM,MAAM;AAAA,EAC9C,EAAE,WAAW,UAAU,MAAM,OAAO,MAAM,IAAI;AAAA,EAC9C,EAAE,WAAW,UAAU,MAAM,KAAK,MAAM,OAAO;AAAA,EAC/C,EAAE,WAAW,UAAU,MAAM,QAAQ,MAAM,IAAI;AACnD;AAEA,IAAMC,UAAS,gBAAgB,QAAQ,UAAU;AAAA,EAC7C,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,YAAY;AAChB,CAAC;AAED,IAAIC;AAEG,IAAMC,aAAY,MAAMD,aAAYF,SAAO,gBAAgB,QAAQ,gBAAgB,GAAGC,SAAQ;AAAA,EACjG,OAAO;AAAA,IACH,oCAAoC,CAAC,SAAS,YAAY;AAAA,IAC1D,oCAAoC,CAAC,SAAS,YAAY;AAAA,IAC1D,2BAA2B,CAAC,SAAS,iBAAiB;AAAA,IACtD,gCAAgC;AAAA,IAChC,6CAA6C,CAAC,SAAS,YAAY;AAAA,IACnE,oCAAoC;AAAA,IACpC,qCAAqC,CAAC,SAAS,qBAAqB;AAAA,IACpE,qCAAqC,CAAC,SAAS,MAAM;AAAA,IACrD,uCAAuC;AAAA,IACvC,wCAAwC;AAAA,IACxC,0BAA0B;AAAA,IAC1B,wCAAwC;AAAA,IACxC,yBAAyB,CAAC,SAAS,eAAe;AAAA,IAClD,oCAAoC;AAAA,IACpC,6BAA6B;AAAA,IAC7B,8BAA8B,CAAC,SAAS,MAAM;AAAA,IAC9C,mCAAmC,CAAC,SAAS,wBAAwB;AAAA,IACrE,gCAAgC,CAAC,SAAS,OAAO;AAAA,IACjD,uCAAuC;AAAA,IACvC,iCAAiC;AAAA,IACjC,4BAA4B;AAAA,IAC5B,+CAA+C,CAAC,SAAS,OAAO;AAAA,IAChE,mCAAmC,CAAC,SAAS,EAAE,YAAY,MAAM,WAAW,KAAK,CAAC;AAAA,IAClF,sCAAsC,CAAC,SAAS,EAAE,8BAA8B,KAAK,CAAC;AAAA,IACtF,2CAA2C;AAAA,IAC3C,iCAAiC,CAAC,SAAS,OAAO;AAAA,IAClD,8CAA8C,CAAC,SAAS,GAAG,kCAAkC;AAAA,IAC7F,yBAAyB;AAAA,IACzB,mCAAmC;AAAA,IACnC,yBAAyB;AAAA,IACzB,sBAAsB;AAAA,EAC1B;AACJ,CAAC;;;AC9FD,SAAS,UAAAG,gBAAc;AACvB,OAAO,cAAc;AAErB,IAAMC,SAA4B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,iCAAiC,CAAC,SAAS,EAAE,SAAS,eAAe,CAAC;AAAA,EACtE,qCAAqC,CAAC,SAAS,EAAE,aAAa,yBAAyB,CAAC;AAAA,EACxF,iDAAiD;AAAA,EACjD,kDAAkD;AAAA,EAClD,8CAA8C,CAAC,SAAS,EAAE,yBAAyB,OAAO,QAAQ,gBAAgB,UAAU,sBAAsB,CAAC;AAAA,EACnJ,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,wCAAwC;AAAA,EACxC,wCAAwC;AAAA,EACxC,sCAAsC;AAAA,EACtC,0CAA0C;AAAA,EAC1C,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,qDAAqD;AAAA,EACrD,+CAA+C;AAAA,EAC/C,qCAAqC,CAAC,SAAS,EAAE,MAAM,OAAO,mBAAmB,MAAM,cAAc,OAAO,2BAA2B,MAAM,gCAAgC,MAAM,MAAM,OAAO,mBAAmB,MAAM,oBAAoB,KAAK,CAAC;AAAA,EACnP,2CAA2C,CAAC,SAAS,EAAE,SAAS,OAAO,WAAW,OAAO,WAAW,KAAK,CAAC;AAAA,EAC1G,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,yCAAyC;AAC7C;AAEA,IAAM,mBAAuC;AAAA,EACzC,iDAAiD;AAAA,EACjD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,mDAAmD;AAAA,EACnD,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,qDAAqD;AAAA,EACrD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,iDAAiD,CAAC,SAAS,EAAE,oBAAoB,KAAK,CAAC;AAAA,EACvF,oDAAoD,CAAC,SAAS,EAAE,aAAa,MAAM,cAAc,MAAM,UAAU,MAAM,cAAc,MAAM,aAAa,MAAM,YAAY,KAAK,CAAC;AAAA,EAChL,6DAA6D;AAAA,EAC7D,qCAAqC;AAAA,EACrC,mDAAmD;AACvD;AAEA,IAAMC,iBAAgB,CAAC,SAAS,QAAQ,mBAAmB,SAAS,QAAQ,QAAQ,SAAS,QAAQ,SAAS;AAC9G,IAAM,2BAA2B,CAAC,SAAS,QAAQ,mBAAmB,SAAS,QAAQ,oBAAoB;AAO3G,IAAM,kBAAkB,CAAC,EAAE,cAAc,gBAAgB,OAAkC;AAAA,EACvF,eAAe;AAAA,IACX,SAAS;AAAA,IACT;AAAA,IACA,gBAAgB;AAAA,EACpB;AACJ;AAEA,IAAM,qBAAoC;AAAA,EACtC,OAAO,CAAC,WAAW;AAAA,EACnB,OAAO;AAAA,IACH,wBAAwB;AAAA,EAC5B;AACJ;AAEA,IAAM,gBAA+B;AAAA,EACjC,OAAO,CAAC,WAAW,UAAU;AAAA,EAC7B,OAAO;AAAA,IACH,yCAAyC;AAAA,IACzC,sCAAsC;AAAA,EAC1C;AACJ;AAOO,SAAS,WAAW,EAAE,gBAAgB,CAAC,GAAG,WAAW,EAAE,cAAc,iBAAiB,iBAAiB,QAAQ,IAAI,EAAE,EAAE,IAAuB,CAAC,GAA6B;AAC/K,QAAM,QAAQ,CAAC,yBAAyB,GAAG,cAAc,IAAI,CAAC,QAAQ,QAAQ,GAAG,EAAE,CAAC;AACpF,QAAM,cAAc,CAAC,oBAAoB,aAAa;AAEtD,cAAY,KAAK;AAAA,IACb;AAAA,IACA,iBAAiB;AAAA,MACb,eAAe;AAAA,QACX,qBAAqB,cAAc,IAAI,CAAC,QAAQ,IAAI,GAAG,EAAE;AAAA,QACzD,YAAY;AAAA,QACZ,oCAAoC;AAAA,MACxC;AAAA,IACJ;AAAA,EACJ,CAAC;AAED,MAAIC;AAEJ,MAAI,UAAU;AACV,IAAAA,WAAUH,SAAO,GAAG,0BAA0B,EAAE,iBAAiB,gBAAgB,QAAQ,EAAE,GAAG,EAAE,OAAO,EAAE,GAAGC,QAAO,GAAG,iBAAiB,EAAE,GAAG,GAAG,WAAW;AAAA,EAC9J,OAAO;AACH,IAAAE,WAAUH,SAAO,GAAGE,gBAAe,EAAE,OAAAD,OAAM,GAAG,GAAG,WAAW;AAAA,EAChE;AAEA,SAAOE,SAAQ,KAAK,CAAC,MAAM,EAAE,IAAI,CAACC,aAAY,EAAE,GAAGA,SAAQ,MAAM,EAAE,CAAC;AACxE;;;AC3HA,SAAS,UAAAC,gBAAc;AACvB,OAAOC,aAAY;AAEZ,IAAM,gBAAgB,MAAMD;AAAA,EAC/B;AAAA,IACI,SAAS;AAAA,MACL,kBAAkBC;AAAA,IACtB;AAAA,IACA,OAAO;AAAA,MACH,kBAAkB;AAAA,MAClB,qCAAqC;AAAA,MACrC,oCAAoC;AAAA,MACpC,iCAAiC,CAAC,SAAS,EAAE,MAAM,cAAc,mBAAmB,MAAM,MAAM,OAAO,mBAAmB,KAAK,CAAC;AAAA,IACpI;AAAA,EACJ;AAAA,EACA;AAAA,IACI,OAAO,CAAC,WAAW;AAAA,IACnB,OAAO;AAAA,MACH,iCAAiC;AAAA,IACrC;AAAA,EACJ;AACJ;;;ACrBA,SAAS,UAAAC,gBAAc;AACvB,OAAOC,cAAY;AAEZ,IAAM,+BAA+B;AAErC,IAAM,0BAA0B;AAWhC,IAAM,WAAW,CAAC,EAAE,aAAa,8BAA8B,iBAAiB,yBAAyB,GAAG,QAAQ,IAAqB,CAAC,MAAMD,SAAO;AAAA,EAC1J,SAAS;AAAA,IACL,sBAAsBC;AAAA,EAC1B;AAAA,EACA,UAAU;AAAA,IACN,sBAAsB,EAAE,YAAY,gBAAgB,GAAG,QAAQ;AAAA,EACnE;AAAA,EACA,OAAO;AAAA,IACH,GAAGA,SAAO,QAAQ,mBAAmB,EAAE;AAAA,IACvC,gCAAgC;AAAA,IAChC,8CAA8C;AAAA,EAClD;AACJ,CAAC;;;AC3BD,SAAS,kBAAkB;AAC3B,OAAO,sBAAsB;AAC7B,SAAS,UAAAC,gBAAc;AACvB,SAAS,uBAAuB;AAChC,OAAOC,cAAY;AACnB,OAAO,mBAAmB;AAC1B,OAAO,wBAAwB;AAC/B,OAAO,eAAe;AAGtB,eAAsB,oBAAoB;AACtC,QAAM,iBAAqC,CAAC;AAC5C,QAAM,mBAAmB,MAAMC,WAAU;AAEzC,aAAWC,WAAU,kBAAkB;AACnC,QAAIA,QAAO,OAAO;AACd,iBAAW,CAAC,UAAU,WAAW,KAAK,OAAO,QAAQA,QAAO,KAAK,GAAG;AAChE,YAAI,WAAW,WAAW,GAAG;AACzB,gBAAM,OAAO,SAAS,WAAW,aAAa,IAAI,SAAS,QAAQ,eAAe,EAAE,IAAI;AAExF,cAAI,QAAQC,SAAO,OAAO;AACtB,2BAAe,OAAO,IAAI,EAAE,IAAI;AAAA,UACpC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,IAAM,YAAY,gBAAgB;AAAA,EAC9BA,SAAO,WAAW,MAAM;AAAA,EACxB,mBAAmB,EAAE,QAAQ,EAAE,QAAQ,MAAM,cAAc,KAAK,EAAE,CAAC;AACvE,CAAC;AAEM,IAAM,MAAM,YAAYC;AAAA,EAC3BD,SAAO,QAAQ,WAAW;AAAA,EAC1BA,SAAO,QAAQ,gBAAgB;AAAA,EAC/BA,SAAO,QAAQ,2BAA2B;AAAA,EAC1CA,SAAO,QAAQ,kBAAkB;AAAA,EACjC,cAAc,QAAQ,kBAAkB;AAAA,EACxC;AAAA,IACI,OAAO,CAAC,UAAU;AAAA,IAClB;AAAA,IACA,iBAAiB;AAAA,MACb,QAAQ;AAAA,MACR,eAAe;AAAA,QACX,QAAQ;AAAA,QACR,qBAAqB,CAAC,MAAM;AAAA,QAC5B,YAAY;AAAA,QACZ,cAAc;AAAA,UACV,KAAK;AAAA,QACT;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,OAAO;AAAA,MACH,GAAI,MAAM,kBAAkB;AAAA,MAC5B,qBAAqB;AAAA,MACrB,kBAAkB,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,KAAK,EAAE,CAAC;AAAA,MACtD,mBAAmB,CAAC,SAAS,EAAE,OAAO,CAAC,UAAU,YAAY,OAAO,EAAE,CAAC;AAAA,MACvE,yBAAyB,CAAC,SAAS,EAAE,eAAe,GAAG,WAAW,UAAU,YAAY,aAAa,CAAC;AAAA,MACtG,2BAA2B,CAAC,SAAS,CAAC,gBAAgB,aAAa,CAAC;AAAA,MACpE,yCAAyC;AAAA,MACzC,qCAAqC;AAAA,MACrC,gCAAgC,CAAC,SAAS,YAAY;AAAA,MACtD,gCAAgC;AAAA,MAChC,2BAA2B,CAAC,SAAS,EAAE,OAAO,CAAC,iBAAiB,eAAe,eAAe,aAAa,EAAE,CAAC;AAAA,MAC9G,gCAAgC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,oCAAoC,CAAC,SAAS,EAAE,WAAW,UAAU,YAAY,QAAQ,CAAC;AAAA,MAC1F,oCAAoC;AAAA,MACpC,oCAAoC;AAAA,MACpC,2BAA2B,CAAC,SAAS,CAAC;AAAA,MACtC,mBAAmB,CAAC,SAAS,CAAC;AAAA,MAC9B,mBAAmB,CAAC,SAAS,QAAQ;AAAA,MACrC,iCAAiC,CAAC,SAAS,EAAE,YAAY,CAAC,QAAQ,QAAQ,MAAM,EAAE,CAAC;AAAA,MACnF,mCAAmC;AAAA,MACnC,+BAA+B;AAAA,MAC/B,kCAAkC;AAAA,MAClC,uBAAuB;AAAA,MACvB,sCAAsC,CAAC,SAAS,EAAE,iBAAiB,KAAK,CAAC;AAAA,MACzE,qCAAqC;AAAA,MACrC,gCAAgC;AAAA,MAChC,uBAAuB,CAAC,SAAS,WAAW;AAAA,MAC5C,oCAAoC;AAAA,MACpC,0CAA0C,CAAC,SAAS,EAAE,SAAS,CAAC,KAAK,EAAE,CAAC;AAAA,MACxE,qCAAqC;AAAA,MACrC,qCAAqC,CAAC,SAAS,EAAE,SAAS,KAAK,CAAC;AAAA,MAChE,4BAA4B,CAAC,SAAS,OAAO;AAAA,MAC7C,sCAAsC;AAAA,MACtC,qCAAqC;AAAA,MACrC,+BAA+B,CAAC,SAAS,EAAE,SAAS,UAAU,CAAC;AAAA,MAC/D,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,4BAA4B;AAAA,MAC5B,yBAAyB;AAAA,MACzB,iBAAiB;AAAA,MACjB,mCAAmC;AAAA,MACnC,6CAA6C;AAAA,MAC7C,6BAA6B;AAAA,MAC7B,sCAAsC;AAAA,MACtC,oCAAoC;AAAA,MACpC,uCAAuC;AAAA,MACvC,4BAA4B;AAAA,MAC5B,6BAA6B;AAAA,MAC7B,8BAA8B;AAAA,MAC9B,mCAAmC;AAAA,MACnC,6BAA6B;AAAA,MAC7B,iCAAiC;AAAA,MACjC,yBAAyB;AAAA,MACzB,qBAAqB,CAAC,SAAS,GAAG,EAAE,YAAY,GAAG,YAAY,EAAE,CAAC;AAAA,MAClE,+CAA+C;AAAA,MAC/C,6BAA6B;AAAA,MAC7B,4BAA4B;AAAA,MAC5B,oBAAoB;AAAA,IACxB;AAAA,EACJ;AACJ;;;ACrHA,SAAS,4BAA4B;AACrC,SAAS,UAAAE,gBAAc;AACvB,OAAOC,cAAY;AAEnB,IAAM,yBAAkC;AAAA,EACpC,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,2BAA2B;AAAA,EAC3B,gBAAgB;AAAA,EAChB,YAAY,OAAO;AAAA,EACnB,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,wBAAwB;AAAA,EACxB,aAAa;AAAA,EACb,UAAU;AAAA,EACV,eAAe;AAAA,EACf,SAAS;AAAA,EACT,yBAAyB;AAC7B;AAEA,IAAM,kBAAkB,CAAC,OAAiB,kBAA2B,CAAC,OAAsB;AAAA,EACxF;AAAA,EACA,iBAAiB;AAAA,IACb,QAAQA,SAAO;AAAA,EACnB;AAAA,EACA,SAAS;AAAA,IACL,QAAQA;AAAA,EACZ;AAAA,EACA,OAAO;AAAA,IACH,mBAAmB,CAAC,SAAS,EAAE,GAAG,wBAAwB,GAAG,gBAAgB,CAAC;AAAA,EAClF;AACJ;AAgBA,IAAM,oBAAoB,CAAC,EAAE,UAAAC,YAAW,MAAM,IAAsC,CAAC,MAAkD;AACnI,QAAM,kBAAkB,qBAAqBA,SAAQ,KAAK,EAAE,SAAS,MAAM;AAE3E,SAAO;AAAA,IACH,CAAC,CAAC,UAAU,GAAG,EAAE,QAAQ,MAAM,CAAC;AAAA,IAChC,CAAC,CAAC,kBAAkB,GAAG,EAAE,QAAQ,MAAM,CAAC;AAAA,IACxC,CAAC,CAAC,WAAW,GAAG,EAAE,QAAQ,OAAO,CAAC;AAAA,IAClC,CAAC,CAAC,WAAW,GAAG,EAAE,QAAQ,OAAO,CAAC;AAAA,IAClC,CAAC,CAAC,SAAS,GAAG,EAAE,QAAQ,WAAW,CAAC;AAAA,IACpC,CAAC,CAAC,cAAc,GAAG,EAAE,QAAQ,QAAQ,GAAI,gBAAgB,UAAU,EAAE,SAAS,CAAC,2BAA2B,GAAG,GAAG,gBAAgB,IAAI,CAAC,EAAG,CAAC;AAAA,IACzI,CAAC,CAAC,cAAc,GAAG,EAAE,QAAQ,OAAO,CAAC;AAAA,EACzC;AACJ;AAIO,IAAM,SAAS,CAAC,UAAyB,CAAC,MAAMF,SAAO,GAAG,kBAAkB,OAAO,EAAE,IAAI,CAAC,CAAC,OAAO,eAAe,MAAM;AAC1H,SAAO,gBAAgB,OAAO,eAAe;AACjD,CAAC,CAAC;;;ACrEF,OAAOG,cAAY;AAEZ,IAAM,YAAY,MAAMA,SAAO;AAAA,EAClC,QAAQ;AACZ,CAAC;;;ACHD,SAAS,UAAAC,gBAAc;AACvB,OAAOC,cAAY;AAEZ,IAAM,gBAAgB,MAAMD,SAAsB;AAAA,EACrD,SAAS;AAAA,IACL,eAAeC;AAAA,EACnB;AAAA,EACA,OAAO;AAAA,IACH,8BAA8B,CAAC,SAAS,EAAE,OAAO,OAAO,MAAM,UAAU,CAAC;AAAA,IACzE,8BAA8B,CAAC,SAAS,EAAE,QAAQ,CAAC,QAAQ,CAAC,eAAe,gBAAgB,cAAc,eAAe,GAAG,WAAW,YAAY,YAAY,CAAC,UAAU,WAAW,OAAO,GAAG,eAAe,UAAU,SAAS,GAAG,iBAAiB,UAAU,OAAO,OAAO,MAAM,UAAU,CAAC;AAAA,IAC7R,oCAAoC,CAAC,SAAS,EAAE,OAAO,OAAO,MAAM,UAAU,CAAC;AAAA,IAC/E,oCAAoC,CAAC,SAAS,EAAE,OAAO,OAAO,MAAM,UAAU,CAAC;AAAA,IAC/E,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,EACpB;AACJ,CAAC;;;AChBD,SAAS,UAAAC,gBAAc;AACvB,OAAOC,cAAY;AAEZ,IAAM,QAAQ,MAAMD,SAAOC,SAAO,QAAQ,aAAa;AAAA,EAC1D,OAAO;AAAA,IACH,2BAA2B;AAAA,IAC3B,+BAA+B;AAAA,IAC/B,gCAAgC;AAAA,IAChC,gCAAgC;AAAA,IAChC,mBAAmB;AAAA,IACnB,gCAAgC;AAAA,IAChC,mCAAmC;AAAA,IACnC,+BAA+B;AAAA,IAC/B,gCAAgC;AAAA,IAChC,gCAAgC;AAAA,IAChC,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,+BAA+B;AAAA,IAC/B,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,yBAAyB;AAAA,IACzB,kCAAkC;AAAA,IAClC,oBAAoB;AAAA,IACpB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,yBAAyB;AAAA,EAC7B;AACJ,CAAC;;;AC3BD,SAAS,UAAAC,gBAAc;AACvB,OAAOC,cAAY;AAEZ,IAAM,UAAU,MAAMD,SAAOC,SAAO,QAAQ,aAAa;AAAA,EAC5D,OAAO;AAAA,IACH,oCAAoC;AAAA,IACpC,uCAAuC,CAAC,SAAS,EAAE,qBAAqB,MAAM,CAAC;AAAA,IAC/E,yBAAyB,CAAC,SAAS,EAAE,OAAO,EAAE,WAAW,MAAM,YAAY,KAAK,GAAG,QAAQ,CAAC,OAAO,YAAY,EAAE,CAAC;AAAA,IAClH,wBAAwB;AAAA,IACxB,4BAA4B;AAAA,IAC5B,uCAAuC;AAAA,IACvC,yCAAyC;AAAA,IACzC,8BAA8B;AAAA,IAC9B,2BAA2B;AAAA,IAC3B,sCAAsC;AAAA,IACtC,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,2BAA2B;AAAA,IAC3B,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,yBAAyB;AAAA,IACzB,0BAA0B;AAAA,IAC1B,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,mDAAmD;AAAA,IACnD,6BAA6B;AAAA,IAC7B,8BAA8B,CAAC,SAAS,OAAO;AAAA,EACnD;AACJ,CAAC;;;AC3BD,SAAS,wBAAAC,6BAA4B;AACrC,SAAS,gBAAgB;AAYlB,SAAS,aAAa,EAAE,UAAU,YAAY,MAAM,YAAY,oBAAoB,CAAC,GAAG,QAAQ,eAAe,KAAK,cAAc,QAAAC,SAAQ,SAAS,SAAS,IAAyB,CAAC,GAAG;AAC5L,oBAAkB,kBAAkB,CAAC;AAErC,MAAI,cAAc;AACd,sBAAkB,cAAc,KAAK,KAAK;AAAA,EAC9C;AAEA,QAAMC,WAAU,SAAS,QAAQ,GAAG,UAAU,GAAG,WAAW,GAAG,WAAW,iBAAiB,GAAGC,WAAU,GAAG,SAAS,GAAG,QAAQ,GAAG,MAAM,GAAG,KAAK,GAAG,QAAQ,GAAG,OAAO,GAAG,cAAc,GAAG,MAAM,GAAG,IAAI,GAAG,OAAO,aAAa,GAAG,cAAc,GAAG,MAAM,GAAG,QAAQ,CAAC;AAEnQ,MAAI,cAAc;AACd,IAAAD,SAAQ,OAAO,IAAI,CAAC;AAAA,EACxB;AAEA,QAAM,kBAAkBE,sBAAqB,SAAS;AAEtD,MAAI,oBAAoB,OAAO;AAC3B,IAAAF,SAAQ,OAAO,SAAS,eAAe,CAAC;AAAA,EAC5C;AAEA,MAAID,SAAQ;AACR,IAAAC,SAAQ,OAAOD,OAAM;AAAA,EACzB;AAEA,MAAI,UAAU;AACV,IAAAC,SAAQ,OAAO,QAAQ;AAAA,EAC3B;AAEA,SAAOA;AACX;", "names": ["concat", "concat", "concat", "plugin", "concat", "concat", "plugin", "concat", "AST_NODE_TYPES", "node", "AST_NODE_TYPES", "node", "AST_NODE_TYPES", "AST_NODE_TYPES", "node", "ASTUtils", "rules", "node", "ASTUtils", "plugin", "concat", "concat", "plugin", "concat", "plugin", "concat", "plugin", "concat", "plugin", "configs", "stylistic", "concat", "rules", "pluginConfigs", "configs", "config", "concat", "plugin", "concat", "plugin", "concat", "plugin", "stylistic", "config", "plugin", "concat", "concat", "plugin", "tailwind", "plugin", "concat", "plugin", "concat", "plugin", "concat", "plugin", "resolveNestedOptions", "config", "configs", "stylistic", "resolveNestedOptions"]}