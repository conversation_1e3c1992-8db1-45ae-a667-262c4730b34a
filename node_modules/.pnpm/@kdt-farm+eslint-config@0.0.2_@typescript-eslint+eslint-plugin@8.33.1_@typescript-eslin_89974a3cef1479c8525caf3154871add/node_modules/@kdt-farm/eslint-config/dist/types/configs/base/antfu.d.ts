export declare const antfu: () => Promise<({
    plugins: {
        antfu: {
            meta: {
                name: string;
                version: string;
            };
            rules: {
                "consistent-chaining": import("eslint").Rule.RuleModule & {
                    defaultOptions: [{
                        allowLeadingPropertyAccess?: boolean;
                    }];
                };
                "consistent-list-newline": import("eslint").Rule.RuleModule & {
                    defaultOptions: [{
                        ArrayExpression?: boolean;
                        ArrayPattern?: boolean;
                        ArrowFunctionExpression?: boolean;
                        CallExpression?: boolean;
                        ExportNamedDeclaration?: boolean;
                        FunctionDeclaration?: boolean;
                        FunctionExpression?: boolean;
                        ImportDeclaration?: boolean;
                        JSONArrayExpression?: boolean;
                        JSONObjectExpression?: boolean;
                        JSXOpeningElement?: boolean;
                        NewExpression?: boolean;
                        ObjectExpression?: boolean;
                        ObjectPattern?: boolean;
                        TSFunctionType?: boolean;
                        TSInterfaceDeclaration?: boolean;
                        TSTupleType?: boolean;
                        TSTypeLiteral?: boolean;
                        TSTypeParameterDeclaration?: boolean;
                        TSTypeParameterInstantiation?: boolean;
                    }];
                };
                curly: import("eslint").Rule.RuleModule & {
                    defaultOptions: [];
                };
                "if-newline": import("eslint").Rule.RuleModule & {
                    defaultOptions: [];
                };
                "import-dedupe": import("eslint").Rule.RuleModule & {
                    defaultOptions: [];
                };
                "indent-unindent": import("eslint").Rule.RuleModule & {
                    defaultOptions: [{
                        indent?: number;
                        tags?: string[];
                    }];
                };
                "no-import-dist": import("eslint").Rule.RuleModule & {
                    defaultOptions: [];
                };
                "no-import-node-modules-by-path": import("eslint").Rule.RuleModule & {
                    defaultOptions: [];
                };
                "no-top-level-await": import("eslint").Rule.RuleModule & {
                    defaultOptions: [];
                };
                "no-ts-export-equal": import("eslint").Rule.RuleModule & {
                    defaultOptions: [];
                };
                "top-level-function": import("eslint").Rule.RuleModule & {
                    defaultOptions: [];
                };
            };
        };
    };
    rules: {
        'antfu/if-newline': "error";
        'antfu/import-dedupe': "error";
        'antfu/no-import-dist': "error";
        'antfu/no-import-node-modules-by-path': "error";
    };
    files?: undefined;
} | {
    files: string[];
    rules: {
        'antfu/no-import-dist': "off";
        'antfu/no-import-node-modules-by-path': "off";
        'antfu/if-newline'?: undefined;
        'antfu/import-dedupe'?: undefined;
    };
    plugins?: undefined;
})[]>;
//# sourceMappingURL=antfu.d.ts.map