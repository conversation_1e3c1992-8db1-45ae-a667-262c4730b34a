type OptionValues = 'always' | 'never';
export interface OptionObject {
    multiline?: boolean;
    minProperties?: number;
    consistent?: boolean;
}
type OptionObjectOrValues = OptionValues | OptionObject;
export interface OptionLiterals {
    ObjectExpression?: OptionObjectOrValues;
    ObjectPattern?: OptionObjectOrValues;
    ImportDeclaration?: OptionObjectOrValues;
    ExportDeclaration?: OptionObjectOrValues;
}
type Options = OptionObjectOrValues | OptionLiterals;
type RuleOptions = [Options?];
export type ObjectCurlyNewlineRuleOptions = RuleOptions;
export declare const objectCurlyNewline: import("../../..").RuleModule<RuleOptions>;
export {};
//# sourceMappingURL=object-curly-newline.d.ts.map