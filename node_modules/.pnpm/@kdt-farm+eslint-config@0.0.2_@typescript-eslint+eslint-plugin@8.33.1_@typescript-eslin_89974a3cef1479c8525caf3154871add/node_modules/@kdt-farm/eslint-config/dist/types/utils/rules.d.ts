import type { Rule as ESLintRule } from 'eslint';
import { type RuleWithMeta } from '@typescript-eslint/utils/eslint-utils';
export type Rule<O extends readonly unknown[], M extends string> = Readonly<RuleWithMeta<O, M>>;
export type RuleModule<O extends readonly unknown[]> = ESLintRule.RuleModule & {
    defaultOptions: O;
};
export declare function createRule<O extends readonly unknown[], M extends string>(rule: Rule<O, M>): RuleModule<O>;
//# sourceMappingURL=rules.d.ts.map