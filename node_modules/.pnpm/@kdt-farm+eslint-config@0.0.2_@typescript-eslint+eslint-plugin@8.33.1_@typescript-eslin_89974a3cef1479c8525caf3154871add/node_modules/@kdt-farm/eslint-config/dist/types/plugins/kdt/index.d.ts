import type { Linter } from 'eslint';
declare const plugin: {
    meta: {
        name: string;
        version: string;
    };
    rules: {
        'arrow-empty-body-newline': import("../..").RuleModule<[]>;
        'import-single-line': import("../..").RuleModule<[]>;
        'object-curly-newline': import("../..").RuleModule<[(((("never" | "always") | import("./rules").OptionObject) | import("./rules").OptionLiterals) | undefined)?]>;
    };
};
export default plugin;
type RuleDefinitions = typeof plugin['rules'];
export type KDTRuleOptions = {
    [K in keyof RuleDefinitions]: RuleDefinitions[K]['defaultOptions'];
};
export type KDTRules = {
    [K in keyof KDTRuleOptions]: Linter.RuleEntry<KDTRuleOptions[K]>;
};
//# sourceMappingURL=index.d.ts.map