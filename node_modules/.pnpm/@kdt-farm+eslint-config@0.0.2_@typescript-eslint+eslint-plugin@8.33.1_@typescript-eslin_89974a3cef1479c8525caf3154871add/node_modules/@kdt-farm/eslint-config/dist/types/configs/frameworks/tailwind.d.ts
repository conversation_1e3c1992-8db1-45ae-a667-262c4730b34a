export declare const DEFAULT_TAILWIND_ENTRY_POINT = "src/styles/main.css";
export declare const DEFAULT_TAILWIND_CONFIG = "tailwind.config.ts";
export interface TailwindOptions {
    entryPoint?: string;
    tailwindConfig?: string;
    attributes?: string[];
    callees?: string[];
    variables?: string[];
    tags?: string[];
}
export declare const tailwind: ({ entryPoint, tailwindConfig, ...options }?: TailwindOptions) => Promise<{
    plugins: {
        'better-tailwindcss': {
            configs: {
                [x: string]: {
                    name: string;
                    plugins: string[];
                    rules: {
                        [x: string]: "error" | "warn";
                    };
                };
            };
            meta: {
                name: string;
            };
            rules: {};
        };
    };
    settings: {
        'better-tailwindcss': {
            attributes?: string[];
            callees?: string[];
            variables?: string[];
            tags?: string[];
            entryPoint: string;
            tailwindConfig: string;
        };
    };
    rules: {
        'better-tailwindcss/multiline': "off";
        'better-tailwindcss/no-unregistered-classes': "off";
    };
}[]>;
//# sourceMappingURL=tailwind.d.ts.map