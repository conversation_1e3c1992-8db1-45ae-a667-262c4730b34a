import type { <PERSON><PERSON> } from 'eslint';
import { type FormatOptions, type TailwindOptions, type TypescriptOptions } from './configs';
export interface DefineConfigOptions {
    tailwind?: TailwindOptions | boolean;
    typescript?: TypescriptOptions;
    format?: FormatOptions;
    vue?: boolean;
    config?: Linter.Config;
    configs?: Linter.Config[];
}
export declare function defineConfig({ tailwind: tailwind_, typescript: typescriptOptions, format: formatOptions, vue: isVueEnabled, config, configs: configs_ }?: DefineConfigOptions): import("eslint-flat-config-utils").FlatConfigComposer<any, never>;
//# sourceMappingURL=factory.d.ts.map