import type { <PERSON><PERSON> } from 'eslint';
export interface TypescriptLanguageOptions {
    tsconfigPath?: string | string[];
    tsconfigRootDir?: string;
}
export interface TypescriptOptions {
    componentExts?: string[];
    tsconfig?: TypescriptLanguageOptions;
}
export declare function typescript({ componentExts, tsconfig }?: TypescriptOptions): Promise<Linter.Config[]>;
//# sourceMappingURL=typescript.d.ts.map