import type { Linter } from 'eslint';
export interface PrettierTailwindOptions {
    enabled?: boolean;
    tailwindStylesheet?: string;
    tailwindConfig?: string;
    tailwindAttributes?: string[];
    tailwindFunctions?: string[];
    tailwindPreserveWhitespace?: boolean;
    tailwindPreserveDuplicates?: boolean;
}
export interface PrettierGetSupportedFilesOptions {
    tailwind?: PrettierTailwindOptions | boolean;
}
export type FormatOptions = PrettierGetSupportedFilesOptions;
export declare const format: (options?: FormatOptions) => Promise<Linter.Config<Linter.RulesRecord>[]>;
//# sourceMappingURL=format.d.ts.map