{"name": "@kdt-farm/eslint-config", "type": "module", "version": "0.0.2", "description": "ESLint configuration for TypeScript projects", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/kdt-farm/eslint-config", "repository": "github:kdt-farm/eslint-config", "bugs": {"email": "<EMAIL>", "url": "https://github.com/kdt-farm/eslint-config/issues"}, "sideEffects": false, "exports": {".": {"types": "./dist/types/index.d.ts", "require": "./dist/index.cjs", "default": "./dist/index.js"}}, "main": "dist/index.js", "types": "dist/types/index.d.ts", "files": ["dist", "src"], "peerDependencies": {"eslint": ">=9"}, "dependencies": {"@eslint-community/eslint-plugin-eslint-comments": "^4.5.0", "@eslint/js": "^9.28.0", "@kdt310722/utils": "^0.0.19", "@stylistic/eslint-plugin": "^4.4.0", "@typescript-eslint/parser": "^8.33.0", "@typescript-eslint/utils": "^8.33.0", "eslint-config-flat-gitignore": "^2.1.0", "eslint-flat-config-utils": "^2.1.0", "eslint-import-resolver-typescript": "^4.4.2", "eslint-merge-processors": "^2.0.0", "eslint-plugin-antfu": "^3.1.1", "eslint-plugin-better-tailwindcss": "^3.0.0", "eslint-plugin-command": "^3.2.1", "eslint-plugin-format": "^1.0.1", "eslint-plugin-import-x": "^4.15.0", "eslint-plugin-jsonc": "^2.20.1", "eslint-plugin-n": "^17.18.0", "eslint-plugin-perfectionist": "^4.13.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-sonarjs": "^3.0.2", "eslint-plugin-unicorn": "^59.0.1", "eslint-plugin-unused-imports": "^4.1.4", "eslint-plugin-vue": "^10.1.0", "eslint-plugin-vuejs-accessibility": "^2.4.1", "eslint-processor-vue-blocks": "^2.0.0", "globals": "^16.2.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "typescript-eslint": "^8.33.0", "vue-eslint-parser": "^10.1.3"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/config-inspector": "^1.0.2", "@kdt310722/tsconfig": "^1.0.0", "@swc/core": "^1.11.29", "@types/node": "^22.15.29", "changelogen": "^0.6.1", "eslint": "^9.28.0", "execa": "^9.6.0", "jiti": "^2.4.2", "lint-staged": "^16.1.0", "npm-check-updates": "^18.0.1", "only-allow": "^1.2.1", "rimraf": "^6.0.1", "simple-git-hooks": "^2.13.0", "tsup": "^8.5.0", "tsx": "^4.19.4", "typescript": "^5.8.3"}, "publishConfig": {"access": "public"}, "commitlint": {"extends": "@commitlint/config-conventional"}, "simple-git-hooks": {"commit-msg": "npx --no -- commitlint --edit ${1}", "pre-commit": "npx lint-staged"}, "lint-staged": {"*": "eslint --fix"}, "scripts": {"inspect": "eslint-config-inspector", "build": "rimraf dist && tsup && tsc --project ./tsconfig.build.json", "release": "tsx scripts/release.ts && changelogen gh release && pnpm publish", "up": "ncu -i", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "tsc --noEmit", "preinstall": "npx only-allow pnpm"}}