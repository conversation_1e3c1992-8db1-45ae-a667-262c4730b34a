import { interceptors } from 'undici'
import { createNoopInterceptor } from './noop'

export function createRetryInterceptor({ enabled, retries, delay, backoff, jitter, maxDelay }: RetryInterceptorOptions = {}) {
    if (enabled === false) {
        return createNoopInterceptor()
    }

    // Map old interface to new undici retry options
    const retryOptions = {
        maxRetries: retries ?? 3,
        minTimeout: delay ?? 1000,
        maxTimeout: maxDelay ?? 10_000,
        timeoutFactor: backoff ?? 2,
    }

    return interceptors.retry(retryOptions)
}
