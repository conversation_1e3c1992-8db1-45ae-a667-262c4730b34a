import type { RetryFailedAttemptError } from './errors/retry-failed-attempt-error'
import type { HttpMethodType, SenderRequest, SenderResponse } from './types'
import { isNullish, type Nullable } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { resolveNestedOptions } from '@kdt310722/utils/object'
import { type Dispatcher, Pool } from 'undici'
import { parseUrl } from '../urls'
import { HttpMethod } from './constants'
import { formatHttpMethod, formatResponseHeader } from './utils'

export interface SenderRetryOptions {
    enabled?: boolean
    retries?: number
    delay?: number
    backoff?: number
    jitter?: number
    maxDelay?: number
}

export interface SenderOptions {
    pool?: Pool.Options
    retry?: SenderRetryOptions | boolean
    timeout?: number
    method?: HttpMethodType
    headers?: Record<string, string>
}

export type SenderEvents = {
    activeRequests: (count: number) => void
    connections: (count: number) => void
    retry: (error: RetryFailedAttemptError) => void
    request: (request: SenderRequest) => void
    response: (response: SenderResponse) => void
}

export interface SenderSendOptions extends Omit<SenderOptions, 'pool'> {
    signal?: AbortSignal
}

// TODO: Retry mechanism
// TODO: Default Pool options
// TODO: Format response body
// TODO: DNS cache
// TODO: Metadata field for both request and response
export class Sender extends Emitter<SenderEvents> {
    public readonly origin: string
    public readonly path: string

    public activeRequests = 0

    protected readonly pool: Pool
    protected readonly headers: Record<string, string>
    protected readonly retryOptions: SenderRetryOptions
    protected readonly timeout: number
    protected readonly method: Dispatcher.HttpMethod

    protected incrementId = 0n

    public constructor(url: string, { pool, headers = {}, retry = true, timeout = 10_000, method = HttpMethod.POST }: SenderOptions = {}) {
        super()

        const { origin, path } = parseUrl(url)

        this.origin = origin
        this.path = path
        this.pool = this.createPool(origin, pool)
        this.headers = headers
        this.retryOptions = this.resolveRetryOptions(retry)
        this.timeout = timeout
        this.method = formatHttpMethod(method)
    }

    public get connections() {
        return this.pool.stats.connected
    }

    public async send(body?: Nullable<string>, options: SenderSendOptions = {}) {
        this.emit('activeRequests', ++this.activeRequests)

        return this.sendRequest(body, options).finally(() => {
            this.emit('activeRequests', --this.activeRequests)
        })
    }

    protected async sendRequest(body?: Nullable<string>, options: SenderSendOptions = {}) {
        const retryOptions = this.getRequestRetryOptions(options.retry)

        if (!retryOptions.enabled) {
            return this.processSend(body, options)
        }

        // TODO: Send request with retry by wrap process with Retry helper function from other file
    }

    protected async processSend(body?: Nullable<string>, options: SenderSendOptions = {}) {
        const start = process.hrtime.bigint()
        const id = String(++this.incrementId)
        const method = options.method ? formatHttpMethod(options.method) : this.method
        const signal = AbortSignal.any([AbortSignal.timeout(options.timeout ?? this.timeout), ...(options.signal ? [options.signal] : [])])
        const headers = { ...this.headers, ...options.headers }
        const request: SenderRequest = { id, method, headers, body: body ?? undefined }

        this.emit('request', request)

        const response = await this.pool.request({ path: this.path, blocking: false, method, body, headers, signal })
        const responseBody = await response.body.text()
        const result: SenderResponse = { id, status: response.statusCode, headers: formatResponseHeader(response.headers), body: responseBody, took: process.hrtime.bigint() - start }

        this.emit('response', result)

        return result
    }

    protected createPool(origin: string, options: Pool.Options = {}) {
        const pool = new Pool(origin, options)

        pool.on('connect', () => this.emit('connections', pool.stats.connected))
        pool.on('disconnect', () => this.emit(`connections`, pool.stats.connected))

        return pool
    }

    protected getRequestRetryOptions(options?: SenderRetryOptions | boolean) {
        if (isNullish(options)) {
            return this.retryOptions
        }

        return { ...this.retryOptions, ...this.resolveRetryOptions(options) }
    }

    protected resolveRetryOptions(options: SenderRetryOptions | boolean) {
        return resolveNestedOptions(options) || { enabled: false }
    }
}
