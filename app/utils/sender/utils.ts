import type { IncomingHttpHeaders } from 'undici/types/header'
import type { HttpMethodType } from './types'
import { wrap } from '@kdt310722/utils/array'
import { isNullish } from '@kdt310722/utils/common'
import { map } from '@kdt310722/utils/object'

export function formatHttpMethod<T extends HttpMethodType>(method: T) {
    return method.toUpperCase() as Uppercase<T>
}

export function formatResponseHeader(headers: IncomingHttpHeaders) {
    return map(headers, (key, value) => [key, wrap(value).map((i) => (isNullish(i) ? '' : i)).join(',')])
}
