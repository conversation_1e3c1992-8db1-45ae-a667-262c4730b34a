import type { Dispatcher } from 'undici'
import type { HttpMethod } from './constants'

export type HttpMethodString = `${HttpMethod}` | Lowercase<`${HttpMethod}`>

export type HttpMethodType = HttpMethod | HttpMethodString

export interface SenderRequest {
    id: string
    method: Dispatcher.HttpMethod
    headers: Record<string, string>
    body?: string
}

export interface SenderResponse {
    id: string
    status: number
    headers: Record<string, string>
    body: string
    took: bigint
}
