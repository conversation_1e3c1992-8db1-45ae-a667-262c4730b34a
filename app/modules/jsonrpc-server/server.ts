import type { JsonRpcResponseId } from '@kdt310722/rpc'
import type { HttpRequest, HttpResponse, TemplatedApp } from 'uWebSockets.js'
import type { HttpRequestHandler, JsonRpcHttpResponse } from './types'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import { isAbortError } from '@kdt310722/utils/error'
import { Emitter } from '@kdt310722/utils/event'
import { tap } from '@kdt310722/utils/function'
import { stringifyJson } from '@kdt310722/utils/json'
import { createDeferred } from '@kdt310722/utils/promise'
import { App } from 'uWebSockets.js'
import { JsonRpcError, JsonRpcErrorCode } from '../../errors/json-rpc-error'
import { getClientIp } from './utils/ip'
import { getJsonRpcRequest, getRequestHeaders } from './utils/requests'

export interface ServerConfig {
    host: string
    port: number
    httpHandler: HttpRequestHandler
}

export interface ServerOptions {
    maxBodySize?: number
}

export type JsonRpcServerEvents = {
    aborted: (id: string) => void
    end: (id: string, response?: JsonRpcHttpResponse) => void
    error: (id: string, error: JsonRpcError) => void
}

export class JsonRpcServer extends Emitter<JsonRpcServerEvents, true> {
    public readonly host: string
    public readonly port: number
    public readonly app: TemplatedApp

    protected readonly httpRequestHandler: HttpRequestHandler
    protected readonly maxBodySize: number

    protected incrementId = 0n

    public constructor({ host, port, httpHandler }: ServerConfig, { maxBodySize = 1024 * 1024 }: ServerOptions = {}) {
        super()

        this.host = host
        this.port = port
        this.httpRequestHandler = httpHandler
        this.maxBodySize = maxBodySize
        this.app = this.createApp()
    }

    public async start() {
        const started = createDeferred<void>()

        this.app.listen(this.host, this.port, (listenSocket) => (
            listenSocket ? started.resolve() : started.reject(new Error('Failed to start JSON-RPC server'))
        ))

        return started
    }

    protected async handleRequest(res: HttpResponse, req: HttpRequest) {
        const receivedAt = process.hrtime.bigint()
        const receivedAtTimestamp = Date.now()
        const id = String(++this.incrementId)
        const abortController = new AbortController()
        const abortHandler = () => this.emit('aborted', id)

        abortController.signal.addEventListener('abort', abortHandler, { once: true })

        res.onAborted(() => {
            abortController.abort()
        })

        let requestId: string | number | null = null
        let response: JsonRpcHttpResponse | undefined

        try {
            const headers = getRequestHeaders(req)
            const clientIp = getClientIp(req, res)
            const message = await getJsonRpcRequest(res, { maxSize: this.maxBodySize, signal: abortController.signal, handleAbort: false })
            const processTime = process.hrtime.bigint() - receivedAt
            const startExecution = process.hrtime.bigint()

            requestId = message.id
            response = await this.httpRequestHandler({ id, message, headers, clientIp, signal: abortController.signal, processTime })

            response.headers ??= {}
            response.headers['X-Received-At-Timestamp'] = String(receivedAtTimestamp)
            response.headers['X-Execution-Time'] = String(process.hrtime.bigint() - startExecution)
            response.headers['X-Process-Time'] = String(processTime)
        } catch (error) {
            response = this.handleError(id, requestId, error)
        }

        if (!abortController.signal.aborted) {
            res.cork(() => {
                if (isNullish(response)) {
                    res.writeStatus('500 Internal Server Error').end()
                } else {
                    this.handleResponse(res, response)
                }

                abortController.signal.removeEventListener('abort', abortHandler)
                this.emit('end', id, response)
            })
        }
    }

    protected handleResponse(httpResponse: HttpResponse, response: JsonRpcHttpResponse) {
        httpResponse.writeStatus(response.status ?? '200 OK')
        httpResponse.writeHeader('Content-Type', 'application/json')

        for (const [key, value] of Object.entries(response.headers ?? {})) {
            httpResponse.writeHeader(key, value)
        }

        httpResponse.end(stringifyJson(response.body))
    }

    protected handleFallback(response: HttpResponse) {
        response.writeStatus('405 Method Not Allowed').end('Used HTTP Method is not allowed. POST or OPTIONS is required')
    }

    protected handlePostFallback(response: HttpResponse) {
        response.writeStatus('404 Not Found').end()
    }

    protected handleHeadRequest(response: HttpResponse) {
        // TODO: Implement this for health check
        response.writeStatus('204 No Content').end()
    }

    protected handleOptionsRequest(response: HttpResponse) {
        // TODO: Implement this for CORS and preflight
        response.writeStatus('204 No Content').end()
    }

    protected createApp() {
        const app = App()

        app.head('/*', this.handleHeadRequest.bind(this))
        app.options('/*', this.handleOptionsRequest.bind(this))
        app.post('/', this.handleRequest.bind(this))
        app.post('/*', this.handlePostFallback.bind(this))
        app.any('/*', this.handleFallback.bind(this))

        return app
    }

    protected handleError(requestId: string, jsonRpcRequestId: JsonRpcResponseId, error: unknown): JsonRpcHttpResponse | undefined {
        if (isAbortError(error)) {
            return
        }

        const jsonRpcError = tap(error instanceof JsonRpcError ? error : new JsonRpcError(JsonRpcErrorCode.INTERNAL_ERROR, 'Internal server error', { cause: error }), (err) => {
            this.emit('error', requestId, err)
        })

        return { status: `${jsonRpcError?.statusCode ?? 500}${notNullish(jsonRpcError?.statusMessage) ? ` ${jsonRpcError.statusMessage}` : ''}`, body: jsonRpcError.toResponse(jsonRpcRequestId) }
    }
}
